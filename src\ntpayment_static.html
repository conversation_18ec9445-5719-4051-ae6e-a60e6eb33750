<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NTPayment - USDT TRC20 Payment</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            max-width: 600px; 
            margin: 0 auto; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .header { 
            text-align: center; 
            color: #333; 
            margin-bottom: 30px; 
        }
        .payment-info { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
        .wallet-address { 
            background: #e9ecef; 
            padding: 15px; 
            border-radius: 5px; 
            font-family: monospace; 
            word-break: break-all; 
            margin: 10px 0; 
            font-size: 14px; 
            position: relative;
        }
        .amount { 
            font-size: 24px; 
            color: #28a745; 
            font-weight: bold; 
            text-align: center; 
            margin: 20px 0; 
        }
        .warning { 
            background: #fff3cd; 
            border: 1px solid #ffeaa7; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
        .steps { 
            margin: 20px 0; 
        }
        .step { 
            margin: 10px 0; 
            padding: 10px; 
            background: #f8f9fa; 
            border-left: 4px solid #007bff; 
        }
        .copy-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 5px 10px; 
            border-radius: 3px; 
            cursor: pointer; 
            margin-left: 10px; 
            font-size: 12px;
        }
        .copy-btn:hover { 
            background: #0056b3; 
        }
        .status { 
            text-align: center; 
            margin: 20px 0; 
            padding: 15px; 
            background: #d4edda; 
            border: 1px solid #c3e6cb; 
            border-radius: 5px; 
        }
        .error { 
            background: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 USDT TRC20 Payment</h1>
            <p>Payment ID: <strong id="payment-id">Loading...</strong></p>
        </div>
        
        <div class="amount" id="amount-display">Amount to pay: Loading... USDT</div>
        
        <div class="payment-info">
            <h3>📍 Wallet Address (TRC20):</h3>
            <div class="wallet-address" id="wallet-address">
                TYourMainWalletAddressHere123456789
                <button class="copy-btn" onclick="copyToClipboard('TYourMainWalletAddressHere123456789')">Copy</button>
            </div>
            
            <h3>🏷️ Payment Memo/Tag:</h3>
            <div class="wallet-address" id="memo-field">
                <span id="memo-text">Loading...</span>
                <button class="copy-btn" onclick="copyMemo()">Copy</button>
            </div>
        </div>
        
        <div class="warning">
            <strong>⚠️ Important:</strong>
            <ul>
                <li>Send exactly <strong id="amount-warning">Loading...</strong> (TRC20 network)</li>
                <li>Include the memo/tag: <strong id="memo-warning">Loading...</strong></li>
                <li>Use only TRC20 network (Tron blockchain)</li>
                <li>Payment will be confirmed automatically after 19 confirmations</li>
            </ul>
        </div>
        
        <div class="steps">
            <h3>📋 Payment Steps:</h3>
            <div class="step">1. Copy the wallet address above</div>
            <div class="step">2. Open your USDT wallet (TronLink, Trust Wallet, etc.)</div>
            <div class="step">3. Send exactly <span id="amount-step">Loading...</span> to the address</div>
            <div class="step">4. Include the memo/tag: <span id="memo-step">Loading...</span></div>
            <div class="step">5. Wait for confirmation (usually 1-3 minutes)</div>
        </div>
        
        <div class="status">
            <p>Waiting for payment...<br>
            <button onclick="checkPayment()" style="margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Payment Status</button></p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>Payment will be processed automatically.<br>
            You can close this page after sending the payment.</p>
        </div>
    </div>

    <script>
        // Получаем параметры из URL
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Инициализация страницы
        function initPage() {
            var paymentId = getUrlParameter('elid') || getUrlParameter('id') || 'UNKNOWN';
            var amount = getUrlParameter('amount') || '10.00';
            var memo = 'PAY' + paymentId;

            // Обновляем элементы страницы
            document.getElementById('payment-id').textContent = paymentId;
            document.getElementById('amount-display').textContent = 'Amount to pay: ' + amount + ' USDT';
            document.getElementById('memo-text').textContent = memo;
            document.getElementById('amount-warning').textContent = amount + ' USDT';
            document.getElementById('memo-warning').textContent = memo;
            document.getElementById('amount-step').textContent = amount + ' USDT';
            document.getElementById('memo-step').textContent = memo;

            // Если нет параметров, показываем ошибку
            if (paymentId === 'UNKNOWN') {
                document.querySelector('.container').innerHTML = 
                    '<div class="error"><h2>Error: Payment parameters not found</h2>' +
                    '<p>Please return to BillManager and try again.</p>' +
                    '<p>Current URL: ' + window.location.href + '</p></div>';
            }
        }

        // Копирование в буфер обмена
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Скопировано в буфер обмена!');
                });
            } else {
                // Fallback для старых браузеров
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Скопировано в буфер обмена!');
            }
        }

        function copyMemo() {
            var memo = document.getElementById('memo-text').textContent;
            copyToClipboard(memo);
        }

        // Проверка статуса платежа
        function checkPayment() {
            document.querySelector('.status').innerHTML = 
                '<p style="color: #007bff;">Проверяем платеж...</p>';
            
            setTimeout(function() {
                document.querySelector('.status').innerHTML = 
                    '<p>Платеж не найден. Убедитесь, что отправили правильную сумму с указанным memo.<br>' +
                    '<button onclick="checkPayment()" style="margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Again</button></p>';
            }, 2000);
        }

        // Инициализируем страницу при загрузке
        window.onload = initPage;
    </script>
</body>
</html>
