<?php
/**
 * NTPayment Utility Functions for BillManager 6
 * Provides functions for working with USDT TRC20 tokens on Tron blockchain
 */

set_include_path(get_include_path() . PATH_SEPARATOR . "/usr/local/mgr5/include/php");
define('__MODULE__', "pmusdt");
require_once 'bill_util.php';

class USDTUtil {

    private $nodeUrl;
    private $apiKey;
    private $usdtContractAddress;
    private $walletAddress;
    private $privateKey;

    // Default USDT TRC20 contract address on Tron mainnet
    const DEFAULT_USDT_CONTRACT = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

    public function __construct($nodeUrl, $usdtContractAddress, $walletAddress, $privateKey, $apiKey = '') {
        $this->nodeUrl = rtrim($nodeUrl, '/');
        $this->apiKey = $apiKey;
        $this->usdtContractAddress = $usdtContractAddress ?: self::DEFAULT_USDT_CONTRACT;
        $this->walletAddress = $walletAddress;
        $this->privateKey = $privateKey;
    }
    
    /**
     * Make API call to Tron node
     */
    private function apiCall($endpoint, $data = null, $method = 'GET') {
        $url = $this->nodeUrl . $endpoint;

        $headers = [
            'Content-Type: application/json'
        ];

        if ($this->apiKey) {
            $headers[] = 'TRON-PRO-API-KEY: ' . $this->apiKey;
        }

        $options = [
            'http' => [
                'header' => implode("\r\n", $headers) . "\r\n",
                'method' => $method,
                'timeout' => 30
            ]
        ];

        if ($data && $method === 'POST') {
            $options['http']['content'] = json_encode($data);
        }

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        if ($result === false) {
            throw new Exception("Failed to connect to Tron node: $url");
        }

        $response = json_decode($result, true);

        if (isset($response['Error'])) {
            throw new Exception("Tron API Error: " . $response['Error']);
        }

        return $response;
    }
    
    /**
     * Get current block number
     */
    public function getCurrentBlockNumber() {
        $result = $this->apiCall('/wallet/getnowblock');
        return $result['block_header']['raw_data']['number'] ?? 0;
    }

    /**
     * Get USDT TRC20 balance for address
     */
    public function getUSDTBalance($address) {
        try {
            // Convert address to hex format if needed
            $hexAddress = $this->addressToHex($address);

            $data = [
                'contract_address' => $this->addressToHex($this->usdtContractAddress),
                'function_selector' => 'balanceOf(address)',
                'parameter' => str_pad(substr($hexAddress, 2), 64, '0', STR_PAD_LEFT),
                'owner_address' => $hexAddress
            ];

            $result = $this->apiCall('/wallet/triggerconstantcontract', $data, 'POST');

            if (isset($result['constant_result'][0])) {
                $balance = hexdec($result['constant_result'][0]);
                return $balance / 1000000; // USDT TRC20 has 6 decimals
            }

            return 0;
        } catch (Exception $e) {
            Debug("Error getting USDT balance: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get transaction info by hash
     */
    public function getTransactionInfo($txHash) {
        return $this->apiCall('/wallet/gettransactioninfobyid?value=' . $txHash);
    }

    /**
     * Get transaction by hash
     */
    public function getTransaction($txHash) {
        return $this->apiCall('/wallet/gettransactionbyid?value=' . $txHash);
    }

    /**
     * Convert Tron address to hex format
     */
    private function addressToHex($address) {
        // If already hex, return as is
        if (substr($address, 0, 2) === '0x') {
            return $address;
        }

        // Convert base58 Tron address to hex
        // This is a simplified conversion - in production use proper base58 library
        return '0x' . bin2hex($this->base58Decode($address));
    }

    /**
     * Simple base58 decode (simplified version)
     */
    private function base58Decode($input) {
        // This is a simplified implementation
        // In production, use a proper base58 library like stephenhill/base58
        $alphabet = '**********************************************************';
        $decoded = gmp_init(0);
        $multi = gmp_init(1);

        for ($i = strlen($input) - 1; $i >= 0; $i--) {
            $char = $input[$i];
            $pos = strpos($alphabet, $char);
            if ($pos === false) {
                throw new Exception('Invalid base58 character: ' . $char);
            }
            $decoded = gmp_add($decoded, gmp_mul($multi, $pos));
            $multi = gmp_mul($multi, 58);
        }

        return gmp_export($decoded);
    }
    
    /**
     * Check if transaction is USDT TRC20 transfer to specific address
     */
    public function isUSDTTransferTo($txHash, $toAddress, $expectedAmount) {
        try {
            $txInfo = $this->getTransactionInfo($txHash);
            $tx = $this->getTransaction($txHash);

            if (!$txInfo || !$tx) {
                return false;
            }

            // Check transaction result
            if (isset($txInfo['result']) && $txInfo['result'] !== 'SUCCESS') {
                return false;
            }

            // Check if this is a TRC20 transfer
            if (!isset($txInfo['log']) || empty($txInfo['log'])) {
                return false;
            }

            // Parse TRC20 transfer events
            foreach ($txInfo['log'] as $log) {
                if (isset($log['address']) &&
                    strtolower($this->addressToHex($log['address'])) === strtolower($this->addressToHex($this->usdtContractAddress))) {

                    // Check for Transfer event
                    if (isset($log['topics']) && count($log['topics']) >= 3) {
                        // Transfer event signature for TRC20
                        $transferSignature = 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';

                        if (strtolower($log['topics'][0]) === $transferSignature) {
                            // Extract 'to' address from topics[2]
                            $to = '0x' . substr($log['topics'][2], -40);

                            // Extract amount from data
                            $amount = 0;
                            if (isset($log['data'])) {
                                $amount = hexdec($log['data']) / 1000000; // USDT TRC20 has 6 decimals
                            }

                            if (strtolower($to) === strtolower($this->addressToHex($toAddress)) &&
                                $amount >= $expectedAmount) {

                                $currentBlock = $this->getCurrentBlockNumber();
                                $txBlock = isset($txInfo['blockNumber']) ? $txInfo['blockNumber'] : 0;
                                $confirmations = $currentBlock - $txBlock;

                                return [
                                    'amount' => $amount,
                                    'confirmations' => $confirmations
                                ];
                            }
                        }
                    }
                }
            }

            return false;
        } catch (Exception $e) {
            Debug("Error checking USDT TRC20 transfer: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate QR code for payment
     */
    public function generatePaymentQR($address, $amount) {
        // Tron payment URI format
        $paymentUri = "tron:" . $address . "?amount=" . $amount . "&token=USDT";

        // Simple QR code generation using Google Charts API
        $qrUrl = "https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=" . urlencode($paymentUri);

        return $qrUrl;
    }

    /**
     * Validate Tron address
     */
    public function isValidAddress($address) {
        // Tron addresses start with 'T' and are 34 characters long
        return preg_match('/^T[A-Za-z0-9]{33}$/', $address);
    }

    /**
     * Get account info
     */
    public function getAccountInfo($address) {
        try {
            $data = ['address' => $this->addressToHex($address)];
            return $this->apiCall('/wallet/getaccount', $data, 'POST');
        } catch (Exception $e) {
            Debug("Error getting account info: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Monitor payment for specific address and amount
     */
    public function monitorPayment($paymentId, $address, $expectedAmount, $timeoutMinutes = 60) {
        $startTime = time();
        $timeout = $timeoutMinutes * 60;
        
        while (time() - $startTime < $timeout) {
            try {
                // Get recent transactions to the address
                // This is a simplified version - in production you might want to use event logs
                $balance = $this->getUSDTBalance($address);
                
                if ($balance >= $expectedAmount) {
                    Debug("Payment detected for payment ID: $paymentId, amount: $balance USDT");
                    return true;
                }
                
                sleep(30); // Check every 30 seconds
            } catch (Exception $e) {
                Debug("Error monitoring payment: " . $e->getMessage());
                sleep(60); // Wait longer on error
            }
        }
        
        return false; // Timeout reached
    }
}

/**
 * Helper function to create USDT utility instance from payment method parameters
 */
function createUSDTUtil($paymethodParams) {
    return new USDTUtil(
        $paymethodParams['tron_node_url'],
        $paymethodParams['usdt_contract_address'],
        $paymethodParams['wallet_address'],
        $paymethodParams['wallet_private_key'],
        $paymethodParams['tron_api_key'] ?? ''
    );
}

/**
 * Generate unique payment address (for this example, we'll use the main wallet address)
 * In production, you might want to generate unique addresses for each payment
 */
function generatePaymentAddress($paymentId, $walletAddress) {
    // For simplicity, we'll use the main wallet address
    // In production, consider generating unique addresses or using payment IDs
    return $walletAddress;
}

/**
 * Store payment monitoring data
 */
function storePaymentData($paymentId, $address, $amount, $timeout) {
    $data = [
        'payment_id' => $paymentId,
        'address' => $address,
        'amount' => $amount,
        'timeout' => $timeout,
        'created' => time()
    ];
    
    $filename = "/tmp/usdt_payment_" . $paymentId . ".json";
    file_put_contents($filename, json_encode($data));
}

/**
 * Get payment monitoring data
 */
function getPaymentData($paymentId) {
    $filename = "/tmp/usdt_payment_" . $paymentId . ".json";
    
    if (file_exists($filename)) {
        $data = json_decode(file_get_contents($filename), true);
        return $data;
    }
    
    return null;
}

/**
 * Remove payment monitoring data
 */
function removePaymentData($paymentId) {
    $filename = "/tmp/usdt_payment_" . $paymentId . ".json";
    
    if (file_exists($filename)) {
        unlink($filename);
    }
}

?>
