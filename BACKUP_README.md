# 🔄 NTPayment Module Backups

## 📦 Рабочие бэкапы модуля (19.06.2025 11:35)

### 🎯 Основной бэкап с сервера
**Файл:** `ntpayment-working-backup-20250619-113539.tar.gz`

**Содержимое:**
- `/usr/local/mgr5/paymethods/pmntpayment` - рабочий исполняемый модуль
- `/usr/local/mgr5/libexec/pmntpayment.so` - библиотека модуля  
- `/usr/local/mgr5/etc/xml/billmgr_mod_pmntpayment.xml` - XML конфигурация

**Статус:** ✅ РАБОЧИЙ - модуль успешно загружается в BillManager без ошибок

### 📝 Исходный код
**Файл:** `src/ntpayment-working-source-20250619-113633.cpp`

**Особенности:**
- Убран HTTP заголовок `Content-Type: text/xml`
- Правильная обработка всех команд BillManager
- Валидация параметров
- Поддержка всех функций: pmvalidate, pmtune, crtune, crvalidate, crset, crdelete

### 🗂️ Полный проект
**Файл:** `ntpayment-project-backup-20250619-113726.tar.gz`

**Содержимое:** Весь проект со всеми исходниками, XML файлами и документацией

## 🚀 Восстановление из бэкапа

### На сервере BillManager:
```bash
# Распаковать бэкап
tar -xzf ntpayment-working-backup-20250619-113539.tar.gz

# Скопировать файлы
cp paymethods/pmntpayment /usr/local/mgr5/paymethods/
cp libexec/pmntpayment.so /usr/local/mgr5/libexec/
cp etc/xml/billmgr_mod_pmntpayment.xml /usr/local/mgr5/etc/xml/

# Установить права
chmod +x /usr/local/mgr5/paymethods/pmntpayment

# Перезапустить BillManager
pkill -f billmgr && sleep 3 && /usr/local/mgr5/bin/core billmgr &
```

### Компиляция из исходников:
```bash
# Скомпилировать модуль
g++ -std=c++11 -Wall -O2 -o pmntpayment ntpayment-working-source-20250619-113633.cpp
strip pmntpayment

# Создать .so файл
g++ -std=c++11 -Wall -O2 -fPIC -shared -o pmntpayment.so ntpayment-working-source-20250619-113633.cpp
strip pmntpayment.so
```

## ✅ Проверенная функциональность

- ✅ Модуль загружается без ошибок XML
- ✅ Отвечает на все команды BillManager
- ✅ Правильная валидация параметров
- ✅ Поддержка конфигурации через --command config
- ✅ Совместимость с BillManager 6

## 📋 XML структура

Используется правильная структура по образцу Platbox:
- `payment.edit.ntpayment` (type="include") - для редактирования платежа
- `paymethod.edit.ntpayment` (type="form") - для настроек способа оплаты
- Сообщения для `payment.add.pay` - для функции добавления платежа

## 🔧 Технические детали

**Компилятор:** g++ с флагами `-std=c++11 -Wall -O2`
**Архитектура:** x86_64 Linux
**BillManager версия:** 6.x
**Дата создания:** 19.06.2025 11:35 UTC

---
**⚠️ ВАЖНО:** Эти бэкапы содержат последнюю рабочую версию модуля. Используйте их для восстановления в случае проблем.
