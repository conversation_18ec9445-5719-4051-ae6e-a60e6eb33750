#include <iostream>
#include <string>
#include <map>
#include <sstream>
#include <cstdlib>
#include <fstream>
#include <cstring>
#include <ctime>

/**
 * NTPayment CGI for BillManager 6
 * Handles payment redirect and displays payment page
 */

class NTPaymentCGI {
private:
    std::map<std::string, std::string> params;
    
    void parseQueryString() {
        char* queryString = getenv("QUERY_STRING");
        if (!queryString) return;
        
        std::string query(queryString);
        std::istringstream iss(query);
        std::string pair;
        
        while (std::getline(iss, pair, '&')) {
            size_t pos = pair.find('=');
            if (pos != std::string::npos) {
                std::string key = pair.substr(0, pos);
                std::string value = pair.substr(pos + 1);
                params[key] = value;
            }
        }
    }
    
    std::string getParam(const std::string& key, const std::string& defaultValue = "") {
        auto it = params.find(key);
        return (it != params.end()) ? it->second : defaultValue;
    }
    
    void outputHTML(const std::string& content) {
        std::cout << "Content-Type: text/html\r\n\r\n";
        std::cout << content;
    }
    
    std::string generatePaymentPage(const std::string& paymentId) {
        std::string html =
            "<!DOCTYPE html>\n"
            "<html lang=\"en\">\n"
            "<head>\n"
            "    <meta charset=\"UTF-8\">\n"
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
            "    <title>NTPayment</title>\n"
            "    <style>\n"
            "        body {\n"
            "            font-family: Arial, sans-serif;\n"
            "            max-width: 600px;\n"
            "            margin: 0 auto;\n"
            "            padding: 20px;\n"
            "            background-color: #f5f5f5;\n"
            "        }\n"
            "        .payment-container {\n"
            "            background: white;\n"
            "            border-radius: 10px;\n"
            "            padding: 30px;\n"
            "            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n"
            "        }\n"
            "        .header {\n"
            "            text-align: center;\n"
            "            color: #333;\n"
            "            margin-bottom: 30px;\n"
            "        }\n"
            "        .amount {\n"
            "            font-size: 24px;\n"
            "            font-weight: bold;\n"
            "            color: #28a745;\n"
            "            text-align: center;\n"
            "            margin-bottom: 20px;\n"
            "        }\n"
            "        .address-container {\n"
            "            background: #fff;\n"
            "            border: 2px solid #007bff;\n"
            "            border-radius: 8px;\n"
            "            padding: 15px;\n"
            "            margin-bottom: 20px;\n"
            "        }\n"
            "        .address {\n"
            "            font-family: monospace;\n"
            "            font-size: 14px;\n"
            "            word-break: break-all;\n"
            "            background: #f8f9fa;\n"
            "            padding: 10px;\n"
            "            border-radius: 4px;\n"
            "            margin-top: 10px;\n"
            "        }\n"
            "        .instructions {\n"
            "            background: #fff3cd;\n"
            "            border: 1px solid #ffeaa7;\n"
            "            border-radius: 8px;\n"
            "            padding: 15px;\n"
            "            margin-bottom: 20px;\n"
            "        }\n"
            "        .copy-btn {\n"
            "            background: #007bff;\n"
            "            color: white;\n"
            "            border: none;\n"
            "            padding: 8px 16px;\n"
            "            border-radius: 4px;\n"
            "            cursor: pointer;\n"
            "            margin-left: 10px;\n"
            "        }\n"
            "        .copy-btn:hover {\n"
            "            background: #0056b3;\n"
            "        }\n"
            "    </style>\n"
            "</head>\n"
            "<body>\n"
            "    <div class=\"payment-container\">\n"
            "        <div class=\"header\">\n"
            "            <h1>NTPayment</h1>\n"
            "            <p>Payment ID: " + paymentId + "</p>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"amount\">10.00 USDT</div>\n"
            "        \n"
            "        <div class=\"address-container\">\n"
            "            <h3>Send USDT TRC20 to this address:</h3>\n"
            "            <div class=\"address\" id=\"paymentAddress\">TYourWalletAddressHere123456789</div>\n"
            "            <button class=\"copy-btn\" onclick=\"copyAddress()\">Copy Address</button>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"instructions\">\n"
            "            <h4>Payment Instructions:</h4>\n"
            "            <ul>\n"
            "                <li>Send exactly <strong>10.00 USDT</strong> to the address above</li>\n"
            "                <li>Make sure to use <strong>TRC20 network</strong> (Tron blockchain)</li>\n"
            "                <li>Do not send any other cryptocurrency or use other networks</li>\n"
            "                <li>Payment will be confirmed after <strong>19</strong> blockchain confirmations</li>\n"
            "            </ul>\n"
            "        </div>\n"
            "        \n"
            "        <div style=\"text-align: center; margin-top: 30px;\">\n"
            "            <p>Having trouble? Contact support with Payment ID: " + paymentId + "</p>\n"
            "        </div>\n"
            "    </div>\n"
            "    \n"
            "    <script>\n"
            "        function copyAddress() {\n"
            "            const addressElement = document.getElementById(\"paymentAddress\");\n"
            "            const textArea = document.createElement(\"textarea\");\n"
            "            textArea.value = addressElement.textContent;\n"
            "            document.body.appendChild(textArea);\n"
            "            textArea.select();\n"
            "            document.execCommand(\"copy\");\n"
            "            document.body.removeChild(textArea);\n"
            "            \n"
            "            const btn = event.target;\n"
            "            const originalText = btn.textContent;\n"
            "            btn.textContent = \"Copied!\";\n"
            "            btn.style.background = \"#28a745\";\n"
            "            setTimeout(() => {\n"
            "                btn.textContent = originalText;\n"
            "                btn.style.background = \"#007bff\";\n"
            "            }, 2000);\n"
            "        }\n"
            "    </script>\n"
            "</body>\n"
            "</html>";
        return html;
    }

public:
    void run() {
        parseQueryString();
        
        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            outputHTML("<html><body><h1>Error: Payment ID not provided</h1></body></html>");
            return;
        }
        
        // Log the request
        std::ofstream log("/var/log/ntpayment_cgi.log", std::ios::app);
        if (log.is_open()) {
            time_t now = time(0);
            char* timeStr = ctime(&now);
            timeStr[strlen(timeStr) - 1] = '\0';
            log << "[" << timeStr << "] Payment CGI called for ID: " << paymentId << std::endl;
            log.close();
        }
        
        std::string html = generatePaymentPage(paymentId);
        outputHTML(html);
    }
};

// BillManager wrapper entry point
extern "C" int ispmain(int argc, char* argv[]) {
    NTPaymentCGI cgi;
    cgi.run();
    return 0;
}

int main() {
    return ispmain(0, nullptr);
}
