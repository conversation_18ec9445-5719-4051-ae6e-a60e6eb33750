#include <iostream>
#include <string>
#include <map>
#include <sstream>
#include <cstdlib>
#include <fstream>
#include <ctime>
#include <cstring>

/**
 * NTPayment CGI Script for BillManager 6
 * Handles payment redirects and displays payment information
 */

class NTPaymentCGI {
private:
    std::map<std::string, std::string> params;
    std::string logFile;

    void log(const std::string& message) {
        std::ofstream log(logFile, std::ios::app);
        if (log.is_open()) {
            time_t now = time(0);
            char* timeStr = ctime(&now);
            timeStr[strlen(timeStr) - 1] = '\0';
            log << "[" << timeStr << "] CGI: " << message << std::endl;
            log.close();
        }
    }

    void parseQueryString() {
        char* query = getenv("QUERY_STRING");
        if (query) {
            std::string queryStr(query);
            std::istringstream iss(queryStr);
            std::string pair;
            
            while (std::getline(iss, pair, '&')) {
                size_t pos = pair.find('=');
                if (pos != std::string::npos) {
                    std::string key = pair.substr(0, pos);
                    std::string value = pair.substr(pos + 1);
                    params[key] = value;
                }
            }
        }
    }

    std::string getParam(const std::string& key, const std::string& defaultValue = "") {
        auto it = params.find(key);
        return (it != params.end()) ? it->second : defaultValue;
    }

    void outputHTML(const std::string& content) {
        std::cout << "Content-Type: text/html; charset=UTF-8\r\n\r\n";
        std::cout << content << std::endl;
    }

    std::string generatePaymentPage() {
        std::string paymentId = getParam("elid");
        
        if (paymentId.empty()) {
            return "<html><body><h1>Error: Payment ID not provided</h1></body></html>";
        }

        log("Generating payment page for ID: " + paymentId);

        // Генерируем временный адрес кошелька (в реальной реализации здесь должна быть генерация нового адреса)
        std::string walletAddress = "TYourGeneratedWalletAddress123456789";
        std::string amount = "10.00";
        std::string usdtContract = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t";

        std::string html =
            "<!DOCTYPE html>\n"
            "<html>\n"
            "<head>\n"
            "    <meta charset=\"UTF-8\">\n"
            "    <title>NTPayment - USDT TRC20 Payment</title>\n"
            "    <style>\n"
            "        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n"
            "        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n"
            "        .header { text-align: center; color: #333; margin-bottom: 30px; }\n"
            "        .payment-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }\n"
            "        .address-box { background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 10px 0; }\n"
            "        .amount { font-size: 24px; color: #28a745; font-weight: bold; text-align: center; margin: 20px 0; }\n"
            "        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
            "        .instructions { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
            "        .qr-placeholder { text-align: center; margin: 20px 0; }\n"
            "        .status { text-align: center; margin: 20px 0; }\n"
            "        .copy-btn { background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; }\n"
            "    </style>\n"
            "    <script>\n"
            "        function copyToClipboard(text) {\n"
            "            navigator.clipboard.writeText(text).then(function() {\n"
            "                alert('Address copied to clipboard!');\n"
            "            });\n"
            "        }\n"
            "        \n"
            "        function checkPaymentStatus() {\n"
            "            console.log('Checking payment status...');\n"
            "        }\n"
            "        \n"
            "        setInterval(checkPaymentStatus, 30000);\n"
            "    </script>\n"
            "</head>\n"
            "<body>\n"
            "    <div class=\"container\">\n"
            "        <div class=\"header\">\n"
            "            <h1>NTPayment - USDT TRC20</h1>\n"
            "            <p>Secure cryptocurrency payment processing</p>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"amount\">\n"
            "            Amount to pay: " + amount + " USDT\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"payment-info\">\n"
            "            <h3>Payment Address (TRC20):</h3>\n"
            "            <div class=\"address-box\">\n"
            "                " + walletAddress + "\n"
            "                <button class=\"copy-btn\" onclick=\"copyToClipboard('" + walletAddress + "')\">Copy</button>\n"
            "            </div>\n"
            "        </div>"
            "\n"
            "        <div class=\"instructions\">\n"
            "            <h3>Payment Instructions:</h3>\n"
            "            <ol>\n"
            "                <li>Open your USDT TRC20 compatible wallet (TronLink, Trust Wallet, etc.)</li>\n"
            "                <li>Send exactly <strong>" + amount + " USDT</strong> to the address above</li>\n"
            "                <li>Make sure you are using the <strong>TRC20 network</strong> (Tron)</li>\n"
            "                <li>Wait for blockchain confirmation (usually 1-3 minutes)</li>\n"
            "                <li>Your payment will be automatically processed</li>\n"
            "            </ol>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"warning\">\n"
            "            <h3>Important:</h3>\n"
            "            <ul>\n"
            "                <li>Send only USDT TRC20 tokens to this address</li>\n"
            "                <li>Do not send any other cryptocurrency</li>\n"
            "                <li>Send the exact amount specified</li>\n"
            "                <li>This address is valid for 60 minutes</li>\n"
            "            </ul>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"qr-placeholder\">\n"
            "            <p>QR Code will be displayed here</p>\n"
            "            <div style=\"width: 200px; height: 200px; border: 2px dashed #ccc; margin: 0 auto; display: flex; align-items: center; justify-content: center;\">\n"
            "                QR Code\n"
            "            </div>\n"
            "        </div>\n"
            "        \n"
            "        <div class=\"status\">\n"
            "            <p><strong>Payment ID:</strong> " + paymentId + "</p>\n"
            "            <p><strong>Status:</strong> <span id=\"status\">Waiting for payment...</span></p>\n"
            "            <p><strong>Contract:</strong> " + usdtContract + "</p>\n"
            "        </div>\n"
            "        \n"
            "        <div style=\"text-align: center; margin-top: 30px;\">\n"
            "            <p><small>Powered by NTPayment | Secure USDT TRC20 Processing</small></p>\n"
            "        </div>\n"
            "    </div>\n"
            "</body>\n"
            "</html>";

        return html;
    }

public:
    NTPaymentCGI() {
        logFile = "/var/log/ntpayment.log";
    }

    void run() {
        log("NTPayment CGI started");
        
        parseQueryString();
        
        try {
            std::string html = generatePaymentPage();
            outputHTML(html);
        } catch (const std::exception& e) {
            log("Exception in CGI: " + std::string(e.what()));
            outputHTML("<html><body><h1>Error processing payment</h1></body></html>");
        }
    }
};

int main() {
    NTPaymentCGI cgi;
    cgi.run();
    return 0;
}
