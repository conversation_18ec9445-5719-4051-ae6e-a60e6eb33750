<?php
$paymentId = $_GET['elid'] ?? $_GET['id'] ?? 'UNKNOWN';
$amount = $_GET['amount'] ?? '10.00';
$walletAddress = 'TYourMainWalletAddressHere123456789';
$memo = 'PAY' . $paymentId;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NTPayment - USDT TRC20 Payment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; max-width: 600px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .payment-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .wallet-address { background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 10px 0; font-size: 14px; }
        .amount { font-size: 24px; color: #28a745; font-weight: bold; text-align: center; margin: 20px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .steps { margin: 20px 0; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .copy-btn { background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 10px; }
        .copy-btn:hover { background: #0056b3; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($paymentId === 'UNKNOWN'): ?>
            <div class="error">
                <h2>Error: Payment parameters not found</h2>
                <p>Please return to BillManager and try again.</p>
                <p>Current URL: <?= htmlspecialchars($_SERVER['REQUEST_URI']) ?></p>
            </div>
        <?php else: ?>
            <div class="header">
                <h1>💰 USDT TRC20 Payment</h1>
                <p>Payment ID: <strong><?= htmlspecialchars($paymentId) ?></strong></p>
            </div>
            
            <div class="amount">Amount to pay: <?= htmlspecialchars($amount) ?> USDT</div>
            
            <div class="payment-info">
                <h3>📍 Wallet Address (TRC20):</h3>
                <div class="wallet-address">
                    <?= htmlspecialchars($walletAddress) ?>
                    <button class="copy-btn" onclick="copyToClipboard('<?= htmlspecialchars($walletAddress) ?>')">Copy</button>
                </div>
                
                <h3>🏷️ Payment Memo/Tag:</h3>
                <div class="wallet-address">
                    <?= htmlspecialchars($memo) ?>
                    <button class="copy-btn" onclick="copyToClipboard('<?= htmlspecialchars($memo) ?>')">Copy</button>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Important:</strong>
                <ul>
                    <li>Send exactly <strong><?= htmlspecialchars($amount) ?> USDT</strong> (TRC20 network)</li>
                    <li>Include the memo/tag: <strong><?= htmlspecialchars($memo) ?></strong></li>
                    <li>Use only TRC20 network (Tron blockchain)</li>
                    <li>Payment will be confirmed automatically after 19 confirmations</li>
                </ul>
            </div>
            
            <div class="steps">
                <h3>📋 Payment Steps:</h3>
                <div class="step">1. Copy the wallet address above</div>
                <div class="step">2. Open your USDT wallet (TronLink, Trust Wallet, etc.)</div>
                <div class="step">3. Send exactly <?= htmlspecialchars($amount) ?> USDT to the address</div>
                <div class="step">4. Include the memo/tag: <?= htmlspecialchars($memo) ?></div>
                <div class="step">5. Wait for confirmation (usually 1-3 minutes)</div>
            </div>
            
            <div style="text-align: center; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">
                <p>Waiting for payment...<br>
                <button onclick="checkPayment()" style="margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Payment Status</button></p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>Payment will be processed automatically.<br>
                You can close this page after sending the payment.</p>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Скопировано в буфер обмена!');
                });
            } else {
                var textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Скопировано в буфер обмена!');
            }
        }

        function checkPayment() {
            var statusDiv = document.querySelector('[style*="background: #d4edda"]');
            statusDiv.innerHTML = '<p style="color: #007bff;">Проверяем платеж...</p>';
            
            setTimeout(function() {
                statusDiv.innerHTML = 
                    '<p>Платеж не найден. Убедитесь, что отправили правильную сумму с указанным memo.<br>' +
                    '<button onclick="checkPayment()" style="margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Check Again</button></p>';
            }, 2000);
        }
    </script>
</body>
</html>
