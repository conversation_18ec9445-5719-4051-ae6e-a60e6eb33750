#include <iostream>
#include <string>
#include <map>
#include <sstream>
#include <cstdlib>
#include <unistd.h>
#include <fstream>
#include <cstring>
#include <ctime>

/**
 * NTPayment Module for BillManager 6 (C++ version)
 * USDT TRC20 payment processing module
 */

class NTPaymentModule {
private:
    std::map<std::string, std::string> params;
    std::string logFile;
    
    void log(const std::string& message) {
        std::ofstream log(logFile, std::ios::app);
        if (log.is_open()) {
            time_t now = time(0);
            char* timeStr = ctime(&now);
            timeStr[strlen(timeStr) - 1] = '\0'; // Remove newline
            log << "[" << timeStr << "] " << message << std::endl;
            log.close();
        }
    }
    
    void parseInput() {
        std::string line;
        while (std::getline(std::cin, line)) {
            if (line.empty()) continue;
            
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);
                params[key] = value;
            }
        }
    }
    
    std::string getParam(const std::string& key, const std::string& defaultValue = "") {
        auto it = params.find(key);
        return (it != params.end()) ? it->second : defaultValue;
    }
    
    void outputXML(const std::string& content) {
        std::cout << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << std::endl;
        std::cout << "<doc>" << std::endl;
        std::cout << content << std::endl;
        std::cout << "</doc>" << std::endl;
    }

    void outputHTML(const std::string& content) {
        std::cout << "Content-Type: text/html\n\n";
        std::cout << content << std::endl;
    }
    
    void handleModuleInfo() {
        log("Handling module info request");

        std::string features =
            "  <feature>\n"
            "    <redirect>on</redirect>\n"
            "    <pmtune>on</pmtune>\n"
            "    <pmvalidate>on</pmvalidate>\n"
            "    <crtune>on</crtune>\n"
            "    <crvalidate>on</crvalidate>\n"
            "    <crset>on</crset>\n"
            "    <crdelete>on</crdelete>\n"
            "  </feature>\n"
            "  <param>\n"
            "    <payment_script>/mancgi/ntpayment</payment_script>\n"
            "  </param>";

        outputXML(features);
    }
    
    void handlePaymentMethodValidate() {
        log("Handling payment method validation");
        
        std::string errors;
        
        // Validate Tron node URL
        std::string nodeUrl = getParam("tron_node_url");
        if (nodeUrl.empty()) {
            errors += "Tron node URL is required; ";
        } else if (nodeUrl.find("http") != 0) {
            errors += "Invalid Tron node URL format; ";
        }
        
        // Validate USDT contract address
        std::string contractAddr = getParam("usdt_contract_address");
        if (contractAddr.empty()) {
            errors += "USDT contract address is required; ";
        } else if (contractAddr.length() != 34 || contractAddr[0] != 'T') {
            errors += "Invalid USDT contract address format; ";
        }
        
        // Validate wallet address
        std::string walletAddr = getParam("wallet_address");
        if (walletAddr.empty()) {
            errors += "Wallet address is required; ";
        } else if (walletAddr.length() != 34 || walletAddr[0] != 'T') {
            errors += "Invalid wallet address format; ";
        }
        
        // Validate private key
        std::string privateKey = getParam("wallet_private_key");
        if (privateKey.empty()) {
            errors += "Private key is required; ";
        } else if (privateKey.length() != 64) {
            errors += "Invalid private key format; ";
        }
        
        if (!errors.empty()) {
            outputXML("  <error>" + errors + "</error>");
        } else {
            outputXML("");
        }
    }
    
    void handlePaymentTune() {
        log("Handling payment tune");
        
        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            outputXML("  <error>Payment ID not provided</error>");
            return;
        }
        
        // For now, return basic payment form
        std::string paymentForm = 
            "  <payment_address>TYourWalletAddressHere123456789</payment_address>\n"
            "  <amount_usdt>10.00 USDT</amount_usdt>\n"
            "  <qr_code><![CDATA[<img src=\"/images/qr_placeholder.png\" alt=\"QR Code\" />]]></qr_code>\n"
            "  <instructions>Send exactly the specified amount of USDT TRC20 to the address above.</instructions>";
            
        outputXML(paymentForm);
    }
    
    void handlePaymentSet() {
        log("Handling payment set");
        
        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            return;
        }
        
        log("Payment set for ID: " + paymentId);
        
        // Start monitoring process
        std::string cmd = "/usr/local/mgr5/cgi/ntnotify " + paymentId + " &";
        system(cmd.c_str());
        
        outputXML("");
    }
    
    void handlePaymentDelete() {
        log("Handling payment delete");

        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            return;
        }

        log("Payment deleted for ID: " + paymentId);
        outputXML("");
    }

    void handleConfigCommand() {
        log("Handling config command");

        // Возвращаем конфигурацию модуля для BillManager
        std::string config =
            "  <ok/>\n"
            "  <feature>\n"
            "    <redirect>on</redirect>\n"
            "    <pmtune>on</pmtune>\n"
            "    <pmvalidate>on</pmvalidate>\n"
            "  </feature>";

        outputXML(config);
    }

    void handlePaymentRedirect() {
        log("Handling payment redirect (CGI mode)");

        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            outputHTML("<html><body><h1>Error: Payment ID not provided</h1></body></html>");
            return;
        }

        log("Processing payment redirect for ID: " + paymentId);

        // Получаем основной кошелек из настроек (в реальной реализации)
        std::string walletAddress = "TYourMainWalletAddressHere123456789";

        // Генерируем уникальный memo/tag для платежа
        std::string memo = "PAY" + paymentId;

        // Сумма платежа (в реальной реализации получаем из BillManager API)
        std::string amount = "10.00"; // USDT

        // Генерируем HTML страницу с инструкциями по оплате
        std::string html =
            "<html>\n"
            "<head>\n"
            "  <title>NTPayment - USDT TRC20 Payment</title>\n"
            "  <meta charset='UTF-8'>\n"
            "  <style>\n"
            "    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }\n"
            "    .container { background: white; padding: 30px; border-radius: 10px; max-width: 600px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n"
            "    .header { text-align: center; color: #333; margin-bottom: 30px; }\n"
            "    .payment-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }\n"
            "    .wallet-address { background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 10px 0; }\n"
            "    .amount { font-size: 24px; color: #28a745; font-weight: bold; text-align: center; margin: 20px 0; }\n"
            "    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
            "    .steps { margin: 20px 0; }\n"
            "    .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }\n"
            "  </style>\n"
            "</head>\n"
            "<body>\n"
            "  <div class='container'>\n"
            "    <div class='header'>\n"
            "      <h1>💰 USDT TRC20 Payment</h1>\n"
            "      <p>Payment ID: " + paymentId + "</p>\n"
            "    </div>\n"
            "    \n"
            "    <div class='amount'>Amount to pay: " + amount + " USDT</div>\n"
            "    \n"
            "    <div class='payment-info'>\n"
            "      <h3>📍 Wallet Address (TRC20):</h3>\n"
            "      <div class='wallet-address'>" + walletAddress + "</div>\n"
            "      \n"
            "      <h3>🏷️ Payment Memo/Tag:</h3>\n"
            "      <div class='wallet-address'>" + memo + "</div>\n"
            "    </div>\n"
            "    \n"
            "    <div class='warning'>\n"
            "      <strong>⚠️ Important:</strong>\n"
            "      <ul>\n"
            "        <li>Send exactly <strong>" + amount + " USDT</strong> (TRC20 network)</li>\n"
            "        <li>Include the memo/tag: <strong>" + memo + "</strong></li>\n"
            "        <li>Use only TRC20 network (Tron blockchain)</li>\n"
            "        <li>Payment will be confirmed automatically after 19 confirmations</li>\n"
            "      </ul>\n"
            "    </div>\n"
            "    \n"
            "    <div class='steps'>\n"
            "      <h3>📋 Payment Steps:</h3>\n"
            "      <div class='step'>1. Copy the wallet address above</div>\n"
            "      <div class='step'>2. Open your USDT wallet (TronLink, Trust Wallet, etc.)</div>\n"
            "      <div class='step'>3. Send exactly " + amount + " USDT to the address</div>\n"
            "      <div class='step'>4. Include the memo/tag: " + memo + "</div>\n"
            "      <div class='step'>5. Wait for confirmation (usually 1-3 minutes)</div>\n"
            "    </div>\n"
            "    \n"
            "    <div style='text-align: center; margin-top: 30px;'>\n"
            "      <p>Payment will be processed automatically.<br>\n"
            "      You can close this page after sending the payment.</p>\n"
            "    </div>\n"
            "  </div>\n"
            "</body>\n"
            "</html>";

        outputHTML(html);
    }

public:
    NTPaymentModule() {
        logFile = "/var/log/ntpayment.log";
    }
    
    void run(int argc, char* argv[]) {
        std::string command = (argc > 1) ? argv[1] : "";

        // Обработка команды --command config
        if (argc >= 3 && command == "--command" && std::string(argv[2]) == "config") {
            log("NTPayment module called with --command config");
            handleConfigCommand();
            return;
        }

        log("NTPayment module called with command: " + command);
        
        // Parse input parameters
        parseInput();
        
        try {
            if (command.empty()) {
                handleModuleInfo();
            } else if (command == "pmvalidate") {
                handlePaymentMethodValidate();
            } else if (command == "pmtune") {
                // Return input as-is for now
                outputXML("");
            } else if (command == "crtune") {
                handlePaymentTune();
            } else if (command == "crvalidate") {
                outputXML("");
            } else if (command == "crset") {
                handlePaymentSet();
            } else if (command == "crdelete") {
                handlePaymentDelete();
            } else if (getParam("elid") != "") {
                // CGI режим - обработка платежа
                handlePaymentRedirect();
            } else {
                log("Unknown command: " + command);
                outputXML("  <error>Unknown command</error>");
            }
        } catch (const std::exception& e) {
            log("Exception: " + std::string(e.what()));
            outputXML("  <error>Module error: " + std::string(e.what()) + "</error>");
        }
    }
};

// BillManager wrapper entry point
extern "C" int ispmain(int argc, char* argv[]) {
    NTPaymentModule module;
    module.run(argc, argv);
    return 0;
}

int main(int argc, char* argv[]) {
    return ispmain(argc, argv);
}
