# Makefile for NTPayment module for BillManager 6

CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2 -fPIC
LDFLAGS =

# Directories
SRC_DIR = src
BUILD_DIR = build
PAYMETHODS_DIR = paymethods
CGI_DIR = cgi

# Targets
MAIN_TARGET = $(PAYMETHODS_DIR)/ntpayment
CGI_TARGET = $(CGI_DIR)/ntpayment
NOTIFY_TARGET = $(CGI_DIR)/ntnotify
SO_TARGET = libexec/pmntpayment.so
CGI_SO_TARGET = libexec/ntpayment.so

# Source files
MAIN_SRC = $(SRC_DIR)/ntpayment.cpp
CGI_SRC = $(SRC_DIR)/ntpayment_cgi.cpp

# Default target
all: $(MAIN_TARGET) $(CGI_TARGET) $(SO_TARGET) $(CGI_SO_TARGET)

# Create directories
$(PAYMETHODS_DIR):
	mkdir -p $(PAYMETHODS_DIR)

$(CGI_DIR):
	mkdir -p $(CGI_DIR)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# Main payment module
$(MAIN_TARGET): $(MAIN_SRC) | $(PAYMETHODS_DIR)
	$(CXX) $(CXXFLAGS) $(LDFLAGS) -o $@ $<
	@if command -v strip >/dev/null 2>&1; then strip $@; fi

# CGI payment handler
$(CGI_TARGET): $(CGI_SRC) | $(CGI_DIR)
	$(CXX) $(CXXFLAGS) $(LDFLAGS) -o $@ $<
	@if command -v strip >/dev/null 2>&1; then strip $@; fi

# Simple notification handler (placeholder)
$(NOTIFY_TARGET): | $(CGI_DIR)
	echo '#!/bin/bash' > $@
	echo 'echo "Content-Type: application/json"' >> $@
	echo 'echo ""' >> $@
	echo 'echo "{\"status\": \"ok\"}"' >> $@
	chmod +x $@

# Shared library for BillManager 6
$(SO_TARGET): $(MAIN_SRC) |
	mkdir -p libexec
	$(CXX) $(CXXFLAGS) -shared $(LDFLAGS) -o $@ $<
	@if command -v strip >/dev/null 2>&1; then strip $@; fi

# CGI shared library
$(CGI_SO_TARGET): $(CGI_SRC) |
	mkdir -p libexec
	$(CXX) $(CXXFLAGS) -shared $(LDFLAGS) -o $@ $<
	@if command -v strip >/dev/null 2>&1; then strip $@; fi

# Install targets
install: all
	@echo "Installing NTPayment module..."
	@if [ -z "$(BILLMGR_PATH)" ]; then \
		echo "Error: BILLMGR_PATH not set. Use: make install BILLMGR_PATH=/path/to/billmanager"; \
		exit 1; \
	fi
	
	# Install XML configuration
	cp etc/xml/billmgr_mod_pmntpayment_final.xml $(BILLMGR_PATH)/etc/xml/billmgr_mod_pmntpayment.xml
	
	# Install shared library
	cp $(SO_TARGET) $(BILLMGR_PATH)/libexec/pmntpayment.so
	chmod 755 $(BILLMGR_PATH)/libexec/pmntpayment.so

	# Create wrapper symlink
	ln -sf ../libexec/wrapper $(BILLMGR_PATH)/paymethods/pmntpayment

	# Install CGI scripts as wrapper links
	ln -sf ../libexec/wrapper $(BILLMGR_PATH)/cgi/ntpayment

	# Install CGI shared library
	cp $(CGI_SO_TARGET) $(BILLMGR_PATH)/libexec/ntpayment.so
	chmod 755 $(BILLMGR_PATH)/libexec/ntpayment.so

	# Install notification script
	cp $(NOTIFY_TARGET) $(BILLMGR_PATH)/cgi/
	chmod 755 $(BILLMGR_PATH)/cgi/ntnotify

	# Install logos
	mkdir -p $(BILLMGR_PATH)/theme/default/images/
	if [ -f "assets/logo/ntpayment_64.png" ]; then \
		cp assets/logo/ntpayment_64.png $(BILLMGR_PATH)/theme/default/images/pmntpayment.png; \
		cp assets/logo/ntpayment_64.png $(BILLMGR_PATH)/theme/default/images/pmntpayment_64.png; \
	fi
	
	# Set ownership
	chown root:root $(BILLMGR_PATH)/etc/xml/billmgr_mod_pmntpayment.xml
	chown root:root $(BILLMGR_PATH)/libexec/pmntpayment.so
	chown root:root $(BILLMGR_PATH)/libexec/ntpayment.so
	chown root:root $(BILLMGR_PATH)/cgi/ntnotify

	# Clear cache
	rm -rf $(BILLMGR_PATH)/var/.xmlcache/*
	
	@echo "Installation completed!"
	@echo "Restart BillManager: $(BILLMGR_PATH)/sbin/mgrctl -m billmgr restart"

# Test compilation
test: all
	@echo "Testing module compilation..."
	@echo "Main module:"
	file $(MAIN_TARGET)
	@echo "CGI module:"
	file $(CGI_TARGET)
	@echo "Testing basic execution:"
	echo "" | ./$(MAIN_TARGET) || echo "Module test completed"

# Clean build files
clean:
	rm -f $(MAIN_TARGET) $(CGI_TARGET) $(NOTIFY_TARGET)
	rm -rf $(BUILD_DIR)

# Development install (local testing)
dev-install: all
	mkdir -p dev_test/paymethods dev_test/cgi dev_test/etc/xml
	cp $(MAIN_TARGET) dev_test/paymethods/
	cp $(CGI_TARGET) dev_test/cgi/
	cp $(NOTIFY_TARGET) dev_test/cgi/
	cp etc/xml/billmgr_mod_ntpayment.xml dev_test/etc/xml/
	@echo "Development installation in dev_test/ directory"

# Check dependencies
check-deps:
	@echo "Checking build dependencies..."
	@which g++ >/dev/null || (echo "Error: g++ not found. Install: apt-get install build-essential" && exit 1)
	@which make >/dev/null || (echo "Error: make not found. Install: apt-get install make" && exit 1)
	@echo "Dependencies OK"

# Help
help:
	@echo "NTPayment Module Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build all components"
	@echo "  install      - Install to BillManager (requires BILLMGR_PATH)"
	@echo "  test         - Test compilation"
	@echo "  clean        - Clean build files"
	@echo "  dev-install  - Install to local dev_test directory"
	@echo "  check-deps   - Check build dependencies"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Usage examples:"
	@echo "  make all"
	@echo "  make install BILLMGR_PATH=/usr/local/mgr5"
	@echo "  make test"

.PHONY: all install test clean dev-install check-deps help
