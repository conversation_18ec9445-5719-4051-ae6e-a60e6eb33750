#include <iostream>
#include <string>
#include <map>
#include <sstream>
#include <cstdlib>
#include <unistd.h>
#include <fstream>
#include <cstring>
#include <ctime>

/**
 * NTPayment Module for BillManager 6 (C++ version)
 * USDT TRC20 payment processing module
 */

class NTPaymentModule {
private:
    std::map<std::string, std::string> params;
    std::string logFile;

    void log(const std::string& message) {
        std::ofstream log(logFile, std::ios::app);
        if (log.is_open()) {
            time_t now = time(0);
            char* timeStr = ctime(&now);
            timeStr[strlen(timeStr) - 1] = '\0'; // Remove newline
            log << "[" << timeStr << "] " << message << std::endl;
            log.close();
        }
    }

    void parseInput() {
        std::string line;
        while (std::getline(std::cin, line)) {
            if (line.empty()) continue;

            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);
                params[key] = value;
            }
        }
    }

    std::string getParam(const std::string& key, const std::string& defaultValue = "") {       
        auto it = params.find(key);
        return (it != params.end()) ? it->second : defaultValue;
    }

    void outputXML(const std::string& content) {
        std::cout << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << std::endl;
        std::cout << "<doc>" << std::endl;
        std::cout << content << std::endl;
        std::cout << "</doc>" << std::endl;
    }

    void handleModuleInfo() {
        log("Handling module info request");

        // Возвращаем информацию о возможностях модуля
        std::string features =
            "  <feature>\n"
            "    <redirect>on</redirect>\n"
            "    <pmtune>on</pmtune>\n"
            "    <pmvalidate>on</pmvalidate>\n"
            "    <crtune>on</crtune>\n"
            "    <crvalidate>on</crvalidate>\n"
            "    <crset>on</crset>\n"
            "    <crdelete>on</crdelete>\n"
            "  </feature>\n"
            "  <param>\n"
            "    <payment_script>/mancgi/ntpayment</payment_script>\n"
            "  </param>";

        outputXML(features);
    }

    void handlePaymentMethodValidate() {
        log("Handling payment method validation");

        std::string errors;

        // Validate Tron node URL
        std::string nodeUrl = getParam("tron_node_url");
        if (nodeUrl.empty()) {
            errors += "Tron node URL is required; ";
        } else if (nodeUrl.find("http") != 0) {
            errors += "Invalid Tron node URL format; ";
        }

        // Validate USDT contract address
        std::string contractAddr = getParam("usdt_contract_address");
        if (contractAddr.empty()) {
            errors += "USDT contract address is required; ";
        } else if (contractAddr.length() != 34 || contractAddr[0] != 'T') {
            errors += "Invalid USDT contract address format; ";
        }

        // Validate wallet address
        std::string walletAddr = getParam("wallet_address");
        if (walletAddr.empty()) {
            errors += "Wallet address is required; ";
        } else if (walletAddr.length() != 34 || walletAddr[0] != 'T') {
            errors += "Invalid wallet address format; ";
        }

        // Validate private key
        std::string privateKey = getParam("wallet_private_key");
        if (privateKey.empty()) {
            errors += "Private key is required; ";
        } else if (privateKey.length() != 64) {
            errors += "Invalid private key format; ";
        }

        if (!errors.empty()) {
            outputXML("  <e>" + errors + "</e>");
        } else {
            outputXML("");
        }
    }

    void handlePaymentTune() {
        log("Handling payment tune");

        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            outputXML("  <e>Payment ID not provided</e>");
            return;
        }

        // For now, return basic payment form
        std::string paymentForm =
            "  <payment_address>TYourWalletAddressHere123456789</payment_address>\n"
            "  <amount_usdt>10.00 USDT</amount_usdt>\n"
            "  <qr_code><![CDATA[<img src=\"/images/qr_placeholder.png\" alt=\"QR Code\" />]]></qr_code>\n"
            "  <instructions>Send exactly the specified amount of USDT TRC20 to the address above.</instructions>";

        outputXML(paymentForm);
    }

    void handlePaymentSet() {
        log("Handling payment set");

        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            return;
        }

        log("Payment set for ID: " + paymentId);

        // Start monitoring process
        std::string cmd = "/usr/local/mgr5/cgi/ntnotify " + paymentId + " &";
        system(cmd.c_str());

        outputXML("");
    }

    void handlePaymentDelete() {
        log("Handling payment delete");

        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            log("Error: Payment ID not provided");
            return;
        }

        log("Payment deleted for ID: " + paymentId);
        outputXML("");
    }

public:
    NTPaymentModule() {
        logFile = "/var/log/ntpayment.log";
    }

    void run(int argc, char* argv[]) {
        std::string command = "";
        
        // Parse command line arguments
        for (int i = 1; i < argc; i++) {
            std::string arg = argv[i];
            if (arg == "--command" && i + 1 < argc) {
                command = argv[i + 1];
                i++; // Skip next argument as it's the command value
            } else if (arg.find("--command=") == 0) {
                command = arg.substr(10); // Remove "--command="
            } else if (i == 1 && arg.find("--") != 0) {
                // First argument without -- prefix is treated as command
                command = arg;
            }
        }

        log("NTPayment module called with command: " + command);

        // Parse input parameters
        parseInput();

        try {
            if (command.empty() || command == "config") {
                handleModuleInfo();
            } else if (command == "pmvalidate") {
                handlePaymentMethodValidate();
            } else if (command == "pmtune") {
                // Return input as-is for now
                outputXML("");
            } else if (command == "crtune") {
                handlePaymentTune();
            } else if (command == "crvalidate") {
                outputXML("");
            } else if (command == "crset") {
                handlePaymentSet();
            } else if (command == "crdelete") {
                handlePaymentDelete();
            } else {
                log("Unknown command: " + command);
                outputXML("  <e>Unknown command</e>");
            }
        } catch (const std::exception& e) {
            log("Exception: " + std::string(e.what()));
            outputXML("  <e>Module error: " + std::string(e.what()) + "</e>");
        }
    }
};

int main(int argc, char* argv[]) {
    NTPaymentModule module;
    module.run(argc, argv);
    return 0;
}

extern "C" int ispmain(int argc, char* argv[]) {
    NTPaymentModule module;
    module.run(argc, argv);
    return 0;
}
