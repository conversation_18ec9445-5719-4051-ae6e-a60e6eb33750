#include <iostream>
#include <cstdio>
#include <cstring>
#include <string>
#include <map>

class NTPaymentModule {
private:
    std::map<std::string, std::string> params;
    
    void parseInput() {
        // Простая заглушка для парсинга входных данных
        char* query = getenv("QUERY_STRING");
        if (query) {
            // Здесь можно добавить парсинг параметров
        }
    }
    
    std::string getParam(const std::string& name) {
        auto it = params.find(name);
        return (it != params.end()) ? it->second : "";
    }
    
    void outputXML(const std::string& content) {
        printf("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        printf("<doc>\n");
        printf("%s\n", content.c_str());
        printf("</doc>\n");
    }

    void outputHTML(const std::string& content) {
        printf("Content-Type: text/html; charset=UTF-8\n\n");
        printf("%s", content.c_str());
    }

    bool isCGIMode() {
        // Проверяем, вызван ли модуль как CGI скрипт
        char* query = getenv("QUERY_STRING");
        char* method = getenv("REQUEST_METHOD");
        return (query != nullptr || method != nullptr);
    }

    void handleCGIRequest() {
        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            paymentId = getParam("id");
            if (paymentId.empty()) {
                paymentId = getParam("payment_id");
            }
        }

        if (paymentId.empty()) {
            outputHTML("<html><body><h1>Error: Payment ID not provided</h1></body></html>");
            return;
        }

        std::string amount = getParam("amount");
        if (amount.empty()) amount = "10.00";

        std::string walletAddress = "TYourMainWalletAddressHere123456789";
        std::string memo = "PAY" + paymentId;

        std::string html =
            "<html>\n"
            "<head>\n"
            "  <title>NTPayment - USDT TRC20 Payment</title>\n"
            "  <meta charset='UTF-8'>\n"
            "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n"
            "  <style>\n"
            "    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }\n"
            "    .container { background: white; padding: 30px; border-radius: 10px; max-width: 600px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n"
            "    .header { text-align: center; color: #333; margin-bottom: 30px; }\n"
            "    .payment-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }\n"
            "    .wallet-address { background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 10px 0; font-size: 14px; }\n"
            "    .amount { font-size: 24px; color: #28a745; font-weight: bold; text-align: center; margin: 20px 0; }\n"
            "    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
            "    .steps { margin: 20px 0; }\n"
            "    .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }\n"
            "  </style>\n"
            "</head>\n"
            "<body>\n"
            "  <div class='container'>\n"
            "    <div class='header'>\n"
            "      <h1>💰 USDT TRC20 Payment</h1>\n"
            "      <p>Payment ID: <strong>" + paymentId + "</strong></p>\n"
            "    </div>\n"
            "    \n"
            "    <div class='amount'>Amount to pay: " + amount + " USDT</div>\n"
            "    \n"
            "    <div class='payment-info'>\n"
            "      <h3>📍 Wallet Address (TRC20):</h3>\n"
            "      <div class='wallet-address'>" + walletAddress + "</div>\n"
            "      \n"
            "      <h3>🏷️ Payment Memo/Tag:</h3>\n"
            "      <div class='wallet-address'>" + memo + "</div>\n"
            "    </div>\n"
            "    \n"
            "    <div class='warning'>\n"
            "      <strong>⚠️ Important:</strong>\n"
            "      <ul>\n"
            "        <li>Send exactly <strong>" + amount + " USDT</strong> (TRC20 network)</li>\n"
            "        <li>Include the memo/tag: <strong>" + memo + "</strong></li>\n"
            "        <li>Use only TRC20 network (Tron blockchain)</li>\n"
            "        <li>Payment will be confirmed automatically after 19 confirmations</li>\n"
            "      </ul>\n"
            "    </div>\n"
            "    \n"
            "    <div class='steps'>\n"
            "      <h3>📋 Payment Steps:</h3>\n"
            "      <div class='step'>1. Copy the wallet address above</div>\n"
            "      <div class='step'>2. Open your USDT wallet (TronLink, Trust Wallet, etc.)</div>\n"
            "      <div class='step'>3. Send exactly " + amount + " USDT to the address</div>\n"
            "      <div class='step'>4. Include the memo/tag: " + memo + "</div>\n"
            "      <div class='step'>5. Wait for confirmation (usually 1-3 minutes)</div>\n"
            "    </div>\n"
            "    \n"
            "    <div style='text-align: center; margin-top: 30px; color: #666;'>\n"
            "      <p>Payment will be processed automatically.<br>\n"
            "      You can close this page after sending the payment.</p>\n"
            "    </div>\n"
            "  </div>\n"
            "</body>\n"
            "</html>";

        outputHTML(html);
    }

public:
    void run(int argc, char* argv[]) {
        std::string command = (argc > 1) ? argv[1] : "";

        parseInput();

        // Если это CGI режим, обрабатываем как веб-запрос
        if (isCGIMode()) {
            handleCGIRequest();
            return;
        }
        
        if (argc > 1 && strcmp(argv[1], "--command") == 0 && argc > 2) {
            if (strcmp(argv[2], "config") == 0) {
                // Конфигурация модуля
                outputXML("  <ok/>\n  <pmntpayment>\n    <name>NTPayment</name>\n    <version>1.0</version>\n  </pmntpayment>");
                return;
            }
        }
        
        if (command.empty()) {
            // Информация о модуле - основные возможности
            std::string features = 
                "  <feature>\n"
                "    <redirect>on</redirect>\n"
                "    <pmtune>on</pmtune>\n"
                "    <pmvalidate>on</pmvalidate>\n"
                "    <crtune>on</crtune>\n"
                "    <crvalidate>on</crvalidate>\n"
                "    <crset>on</crset>\n"
                "    <crdelete>on</crdelete>\n"
                "  </feature>";
            outputXML(features);
        } else if (command == "pmvalidate") {
            // Валидация настроек способа оплаты
            std::string errors;
            
            // Проверяем обязательные поля
            std::string nodeUrl = getParam("tron_node_url");
            std::string contractAddr = getParam("usdt_contract_address");
            std::string walletAddr = getParam("wallet_address");
            std::string privateKey = getParam("wallet_private_key");
            
            if (nodeUrl.empty()) {
                errors += "  <e field=\"tron_node_url\">Укажите URL узла Tron</e>\n";
            }
            if (contractAddr.empty()) {
                errors += "  <e field=\"usdt_contract_address\">Укажите адрес контракта USDT TRC20</e>\n";
            }
            if (walletAddr.empty()) {
                errors += "  <e field=\"wallet_address\">Укажите адрес кошелька</e>\n";
            }
            if (privateKey.empty()) {
                errors += "  <e field=\"wallet_private_key\">Укажите приватный ключ</e>\n";
            }
            
            if (!errors.empty()) {
                outputXML(errors);
            } else {
                outputXML("  <ok/>");
            }
        } else if (command == "pmtune") {
            // Настройка способа оплаты - возвращаем входные данные
            outputXML("  <ok/>");
        } else if (command == "crtune") {
            // Настройка конкретного платежа - возвращаем URL для редиректа
            std::string paymentId = getParam("elid");
            if (paymentId.empty()) {
                outputXML("  <e>Payment ID not provided</e>");
                return;
            }

            std::string amount = getParam("amount");
            if (amount.empty()) amount = "10.00";

            // Возвращаем URL для редиректа
            std::string redirectUrl = "http://localhost:1500/mancgi/ntpayment?elid=" + paymentId + "&amount=" + amount;

            std::string paymentData =
                "  <ok/>\n"
                "  <redirect>" + redirectUrl + "</redirect>";

            outputXML(paymentData);
        } else if (command == "crvalidate") {
            // Валидация платежа
            outputXML("  <ok/>");
        } else if (command == "crset") {
            // Создание/установка платежа
            std::string paymentId = getParam("elid");
            if (paymentId.empty()) {
                outputXML("  <e>Payment ID not provided</e>");
                return;
            }
            
            // Здесь можно запустить мониторинг платежа
            outputXML("  <ok/>");
        } else if (command == "crdelete") {
            // Удаление платежа
            std::string paymentId = getParam("elid");
            if (paymentId.empty()) {
                outputXML("  <e>Payment ID not provided</e>");
                return;
            }
            
            outputXML("  <ok/>");
        } else {
            // Неизвестная команда
            outputXML("  <e>Unknown command: " + command + "</e>");
        }
    }
};

extern "C" int ispmain(int argc, char* argv[]) {
    NTPaymentModule module;
    module.run(argc, argv);
    return 0;
}

extern "C" int main(int argc, char* argv[]) {
    return ispmain(argc, argv);
}
