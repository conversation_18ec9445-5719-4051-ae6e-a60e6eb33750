#!/usr/bin/env python3
"""
NTPayment USDT TRC20 Payment Module for BillManager 6
Based on ISPsystem documentation and NOWPayments structure
"""

import argparse
import datetime as dt
import xml.etree.ElementTree as ET
import sys
import json
import requests
import hashlib
import secrets
import time
from typing import Dict, Any, Optional

sys.path.insert(0, "/usr/local/mgr5/lib/python")

from billmgr.modules.paymethod import (
    PaymethodModule,
    Feature,
    Param,
)
import billmgr.db as db
import billmgr.exception as exc
import billmgr.misc as misc
import billmgr.logger as logging
import billmgr.payment as payment
import billmgr.session as session

MODULE_NAME = "pmntpayment"
logging.init_logging(MODULE_NAME)
logger = logging.get_logger(MODULE_NAME)

class NTPaymentModule(PaymethodModule):
    def __init__(self):
        super().__init__()
        self.set_description("NTPayment USDT TRC20 Payment Gateway")
        
        # Основные возможности модуля согласно документации ISPsystem
        self._add_feature(Feature.REDIRECT)
        self._add_feature(Feature.NOT_PROFILE)
        
        # Обязательные функции согласно документации
        self._add_callable_feature(Feature.PMVALIDATE, self.pm_validate)
        self._add_callable_feature(Feature.PMTUNE, self.pm_tune)
        self._add_callable_feature(Feature.CRTUNE, self.cr_tune)
        self._add_callable_feature(Feature.CRVALIDATE, self.cr_validate)
        self._add_callable_feature(Feature.CRSET, self.cr_set)
        self._add_callable_feature(Feature.CRDELETE, self.cr_delete)
        
        # Дополнительные функции
        self._add_callable_feature(Feature.CHECKPAY, self.check_pay)
        
        # Параметры модуля
        self._add_param(Param.PAYMENT_SCRIPT, "/mancgi/ntpayment")

    def _on_raise_exception(self, args: argparse.Namespace, err: exc.XmlException):
        """Обработка исключений"""
        logger.error(f"Module exception: {err}")
        print(err.as_xml())

    def pm_validate(self):
        """Валидация настроек платежного метода"""
        logger.info("Validating payment method settings")
        
        xml = session.get_input_xml()
        
        # Проверяем обязательные поля
        tron_node_url = xml.findtext("tron_node_url", "").strip()
        if not tron_node_url:
            raise exc.Error("Tron node URL is required")
        
        usdt_contract = xml.findtext("usdt_contract_address", "").strip()
        if not usdt_contract:
            raise exc.Error("USDT contract address is required")
        
        master_wallet = xml.findtext("master_wallet_address", "").strip()
        if not master_wallet:
            raise exc.Error("Master wallet address is required")
        
        private_key = xml.findtext("master_private_key", "").strip()
        if not private_key:
            raise exc.Error("Master private key is required")
        
        # Проверяем подключение к Tron API
        try:
            response = requests.get(f"{tron_node_url}/wallet/getnowblock", timeout=10)
            if response.status_code != 200:
                raise exc.Error("Cannot connect to Tron node")
        except Exception as e:
            raise exc.Error(f"Tron node connection failed: {str(e)}")
        
        logger.info("Payment method validation successful")

    def pm_tune(self):
        """Настройка формы платежного метода"""
        logger.info("Tuning payment method form")
        
        # Возвращаем пустой ответ - форма настраивается через XML
        return ""

    def cr_tune(self):
        """Настройка формы создания платежа"""
        logger.info("Tuning payment creation form")
        
        xml = session.get_input_xml()
        payment_id = xml.findtext("elid", "")
        
        if not payment_id:
            raise exc.Error("Payment ID not provided")
        
        # Получаем информацию о платеже
        payment_info = self._get_payment_info(payment_id)
        amount = payment_info.get("amount", "0.00")
        currency = payment_info.get("currency", "USD")
        
        # Генерируем уникальный кошелек для платежа
        wallet_address = self._generate_payment_wallet(payment_id)
        
        # Сохраняем информацию о платеже в базе
        self._save_payment_data(payment_id, wallet_address, amount)
        
        # Возвращаем HTML с инструкциями по оплате
        html = self._generate_payment_instructions(payment_id, wallet_address, amount)
        
        return html

    def cr_validate(self):
        """Валидация данных платежа"""
        logger.info("Validating payment data")
        
        # Для USDT платежей валидация не требуется
        return ""

    def cr_set(self):
        """Создание платежа"""
        logger.info("Creating payment")
        
        xml = session.get_input_xml()
        payment_id = xml.findtext("elid", "")
        
        if not payment_id:
            raise exc.Error("Payment ID not provided")
        
        # Получаем информацию о платеже
        payment_info = self._get_payment_info(payment_id)
        
        # Генерируем кошелек и сохраняем данные
        wallet_address = self._generate_payment_wallet(payment_id)
        self._save_payment_data(payment_id, wallet_address, payment_info.get("amount", "0"))
        
        # Запускаем мониторинг транзакций
        self._start_payment_monitoring(payment_id)
        
        logger.info(f"Payment {payment_id} created with wallet {wallet_address}")

    def cr_delete(self):
        """Удаление платежа"""
        logger.info("Deleting payment")
        
        xml = session.get_input_xml()
        payment_id = xml.findtext("elid", "")
        
        if payment_id:
            # Удаляем данные платежа из базы
            self._delete_payment_data(payment_id)
            logger.info(f"Payment {payment_id} deleted")

    def check_pay(self):
        """Проверка статуса платежа"""
        logger.info("Checking payment status")
        
        xml = session.get_input_xml()
        payment_id = xml.findtext("elid", "")
        
        if not payment_id:
            return ""
        
        # Проверяем статус платежа в блокчейне
        status = self._check_payment_status(payment_id)
        
        if status == "confirmed":
            # Подтверждаем платеж в BillManager
            self._confirm_payment(payment_id)
        
        return ""

    def _get_payment_info(self, payment_id: str) -> Dict[str, Any]:
        """Получение информации о платеже из BillManager"""
        try:
            # Здесь должен быть запрос к BillManager API
            # Пока возвращаем заглушку
            return {
                "amount": "10.00",
                "currency": "USD",
                "description": f"Payment {payment_id}"
            }
        except Exception as e:
            logger.error(f"Failed to get payment info: {e}")
            return {"amount": "0.00", "currency": "USD"}

    def _generate_payment_wallet(self, payment_id: str) -> str:
        """Генерация уникального кошелька для платежа"""
        # В реальной реализации здесь должна быть генерация нового кошелька
        # Пока возвращаем основной кошелек с уникальным memo
        return f"TYourMainWalletAddress123456789_{payment_id}"

    def _save_payment_data(self, payment_id: str, wallet_address: str, amount: str):
        """Сохранение данных платежа в базе"""
        try:
            # Здесь должно быть сохранение в базу данных
            logger.info(f"Saved payment data: {payment_id}, {wallet_address}, {amount}")
        except Exception as e:
            logger.error(f"Failed to save payment data: {e}")

    def _delete_payment_data(self, payment_id: str):
        """Удаление данных платежа из базы"""
        try:
            # Здесь должно быть удаление из базы данных
            logger.info(f"Deleted payment data for {payment_id}")
        except Exception as e:
            logger.error(f"Failed to delete payment data: {e}")

    def _start_payment_monitoring(self, payment_id: str):
        """Запуск мониторинга платежа"""
        # Здесь должен быть запуск фонового процесса мониторинга
        logger.info(f"Started monitoring for payment {payment_id}")

    def _check_payment_status(self, payment_id: str) -> str:
        """Проверка статуса платежа в блокчейне"""
        # Здесь должна быть проверка транзакций в Tron блокчейне
        return "pending"

    def _confirm_payment(self, payment_id: str):
        """Подтверждение платежа в BillManager"""
        try:
            # Здесь должен быть вызов BillManager API для подтверждения платежа
            logger.info(f"Payment {payment_id} confirmed")
        except Exception as e:
            logger.error(f"Failed to confirm payment: {e}")

    def _generate_payment_instructions(self, payment_id: str, wallet_address: str, amount: str) -> str:
        """Генерация HTML инструкций по оплате"""
        memo = f"PAY{payment_id}"
        
        html = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #333; text-align: center;">💰 USDT TRC20 Payment</h2>
            <p style="text-align: center; color: #666;">Payment ID: {payment_id}</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333;">Amount to pay:</h3>
                <div style="font-size: 24px; font-weight: bold; color: #28a745; text-align: center;">
                    {amount} USDT
                </div>
            </div>
            
            <div style="background: #fff; border: 2px solid #007bff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h3 style="color: #333;">📍 Wallet Address (TRC20):</h3>
                <div style="font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 4px; word-break: break-all;">
                    {wallet_address}
                </div>
                
                <h3 style="color: #333; margin-top: 15px;">🏷️ Payment Memo:</h3>
                <div style="font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                    {memo}
                </div>
            </div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #333;">⚠️ Important Instructions:</h4>
                <ul style="color: #666;">
                    <li>Send exactly <strong>{amount} USDT</strong> to the address above</li>
                    <li>Use <strong>TRC20 network</strong> (Tron blockchain) only</li>
                    <li>Include the memo: <strong>{memo}</strong></li>
                    <li>Payment will be confirmed after 19 confirmations</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>Payment will be processed automatically.<br>
                You can close this page after sending the payment.</p>
            </div>
        </div>
        """
        
        return html


def main():
    """Главная функция модуля"""
    try:
        module = NTPaymentModule()
        module.run()
    except Exception as e:
        logger.error(f"Module error: {e}")
        print(f'<doc><error type="module_error" object=""><param name="value">{e}</param></error></doc>')
        sys.exit(1)


if __name__ == "__main__":
    main()
