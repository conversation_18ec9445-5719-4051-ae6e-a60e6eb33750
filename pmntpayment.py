#!/usr/bin/env python3

import argparse
import datetime as dt
import xml.etree.ElementTree as ET
import sys
import json
import requests

sys.path.insert(0, "/usr/local/mgr5/lib/python")

from billmgr.modules.paymethod import (
    PaymethodModule,
    Feature,
    Param,
)
import billmgr.db as db
import billmgr.exception as exc
import billmgr.misc as misc
import billmgr.logger as logging
import billmgr.payment as payment
import billmgr.session as session

logging.init_logging("pmntpayment")
logger = logging.get_logger("pmntpayment")

PAYMETHOD_MODULE_NAME = "pmntpayment"


class NTPaymentModule(PaymethodModule):
    def __init__(self) -> None:
        super().__init__()
        self.set_description("NTPayment USDT TRC20 Integration")

        # Основные возможности модуля
        self._add_feature(Feature.REDIRECT)
        self._add_feature(Feature.NOT_PROFILE)
        
        # Валидация настроек
        self._add_callable_feature(Feature.PMVALIDATE, self.pm_validate)
        
        # Настройка формы платежа
        self._add_callable_feature(Feature.CRTUNE, self.cr_tune)
        
        # Создание и удаление платежей
        self._add_callable_feature(Feature.CRSET, self.cr_set)
        self._add_callable_feature(Feature.CRDELETE, self.cr_delete)

        # Параметры модуля
        self._add_param(Param.PAYMENT_SCRIPT, "/mancgi/ntpayment.py")

    def pm_validate(self, params: dict) -> None:
        """Валидация настроек платежного метода"""
        logger.info("Validating payment method settings")
        
        # Проверяем обязательные поля
        tron_node_url = params.get("tron_node_url", "")
        if not tron_node_url:
            raise exc.Error("Tron node URL is required")
        
        usdt_contract = params.get("usdt_contract_address", "")
        if not usdt_contract:
            raise exc.Error("USDT contract address is required")
        
        wallet_address = params.get("wallet_address", "")
        if not wallet_address:
            raise exc.Error("Wallet address is required")
        
        private_key = params.get("wallet_private_key", "")
        if not private_key:
            raise exc.Error("Wallet private key is required")
        
        logger.info("Payment method validation successful")

    def cr_tune(self, payment_id: str, params: dict) -> str:
        """Настройка формы платежа - показываем инструкции по оплате"""
        logger.info(f"Tuning payment form for payment {payment_id}")
        
        # Получаем информацию о платеже
        payment_info = payment.get_payment_info(payment_id)
        amount = payment_info.get("paymethodamount", "0.00")
        
        # Генерируем адрес кошелька (в реальности можно генерировать уникальный)
        wallet_address = params.get("wallet_address", "TYourWalletAddressHere")
        
        # Генерируем memo для идентификации платежа
        memo = f"PAY{payment_id}"
        
        # Возвращаем HTML с инструкциями по оплате
        html = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #333; text-align: center;">💰 USDT TRC20 Payment</h2>
            <p style="text-align: center; color: #666;">Payment ID: {payment_id}</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333;">Amount to pay:</h3>
                <div style="font-size: 24px; font-weight: bold; color: #28a745; text-align: center;">
                    {amount} USDT
                </div>
            </div>
            
            <div style="background: #fff; border: 2px solid #007bff; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h3 style="color: #333;">📍 Wallet Address (TRC20):</h3>
                <div style="font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 4px; word-break: break-all;">
                    {wallet_address}
                </div>
                
                <h3 style="color: #333; margin-top: 15px;">🏷️ Payment Memo:</h3>
                <div style="font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                    {memo}
                </div>
            </div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #333;">⚠️ Important Instructions:</h4>
                <ul style="color: #666;">
                    <li>Send exactly <strong>{amount} USDT</strong> to the address above</li>
                    <li>Use <strong>TRC20 network</strong> (Tron blockchain) only</li>
                    <li>Include the memo: <strong>{memo}</strong></li>
                    <li>Payment will be confirmed after 19 confirmations</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; color: #666;">
                <p>Payment will be processed automatically.<br>
                You can close this page after sending the payment.</p>
            </div>
        </div>
        """
        
        return html

    def cr_set(self, payment_id: str, params: dict) -> None:
        """Создание платежа"""
        logger.info(f"Creating payment {payment_id}")
        
        # Здесь можно запустить мониторинг блокчейна
        # Пока просто логируем
        logger.info(f"Payment {payment_id} created successfully")

    def cr_delete(self, payment_id: str, params: dict) -> None:
        """Удаление платежа"""
        logger.info(f"Deleting payment {payment_id}")


def main():
    """Главная функция модуля"""
    try:
        module = NTPaymentModule()
        module.run()
    except Exception as e:
        logger.error(f"Module error: {e}")
        print(f'<doc><error type="module_error" object=""><param name="value">{e}</param></error></doc>')
        sys.exit(1)


if __name__ == "__main__":
    main()
