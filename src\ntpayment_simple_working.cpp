#include <iostream>
#include <string>

// Простой рабочий модуль NTPayment для BillManager 6
// Минимальная реализация с правильной точкой входа

extern "C" int ispmain(int argc, char* argv[]) {
    // Выводим правильную XML структуру согласно документации ISPsystem
    std::cout << "Content-Type: text/xml\n\n";
    std::cout << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    std::cout << "<doc>\n";
    std::cout << "  <feature>\n";
    std::cout << "    <redirect>on</redirect>\n";
    std::cout << "    <pmtune>on</pmtune>\n";
    std::cout << "    <pmvalidate>on</pmvalidate>\n";
    std::cout << "    <crtune>on</crtune>\n";
    std::cout << "    <crvalidate>on</crvalidate>\n";
    std::cout << "    <crset>on</crset>\n";
    std::cout << "    <crdelete>on</crdelete>\n";
    std::cout << "  </feature>\n";
    std::cout << "  <param>\n";
    std::cout << "    <payment_script>/mancgi/ntpayment</payment_script>\n";
    std::cout << "  </param>\n";
    std::cout << "</doc>\n";
    
    return 0;
}

// Для совместимости
int main(int argc, char* argv[]) {
    return ispmain(argc, argv);
}
