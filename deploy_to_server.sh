#!/bin/bash

# Скрипт для быстрого развертывания модуля на сервере BillManager
# Запускайте на сервере: bash deploy_to_server.sh

echo "=== NTPayment Module Deployment ==="

# Переходим в рабочую директорию
cd /opt || { echo "Error: /opt directory not found"; exit 1; }

# Проверяем, есть ли исходники
if [ ! -f "src/ntpayment.cpp" ]; then
    echo "Error: Source files not found in /opt"
    echo "Please upload project files to /opt first"
    exit 1
fi

echo "Building module..."
make clean
make all

if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo "Installing module..."
sudo cp libexec/pmntpayment.so /usr/local/mgr5/libexec/pmntpayment.so
sudo chmod 755 /usr/local/mgr5/libexec/pmntpayment.so
sudo chown root:root /usr/local/mgr5/libexec/pmntpayment.so

echo "Installing XML configuration..."
sudo cp etc/xml/billmgr_mod_pmntpayment_final.xml /usr/local/mgr5/etc/xml/billmgr_mod_pmntpayment.xml
sudo chmod 644 /usr/local/mgr5/etc/xml/billmgr_mod_pmntpayment.xml
sudo chown root:root /usr/local/mgr5/etc/xml/billmgr_mod_pmntpayment.xml

echo "Clearing cache..."
sudo rm -rf /usr/local/mgr5/var/.xmlcache/*

echo "Testing module..."
/usr/local/mgr5/paymethods/pmntpayment --command config

if [ $? -eq 0 ]; then
    echo "✅ Module test successful"
else
    echo "❌ Module test failed"
fi

echo "Restarting BillManager..."
sudo /usr/local/mgr5/sbin/mgrctl -m billmgr exit
sleep 5

echo "Checking logs..."
tail -10 /usr/local/mgr5/var/billmgr.log | grep -i pmntpayment

echo ""
echo "=== Deployment Complete ==="
echo "Module should now be available in BillManager:"
echo "Settings → Payment Methods → Add → NTPayment"
echo ""
echo "To check module status:"
echo "  /usr/local/mgr5/paymethods/pmntpayment --command config"
echo ""
echo "To view logs:"
echo "  tail -f /usr/local/mgr5/var/billmgr.log | grep pmntpayment"
