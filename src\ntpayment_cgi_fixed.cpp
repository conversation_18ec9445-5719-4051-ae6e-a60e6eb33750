#include <iostream>
#include <cstdio>
#include <cstring>
#include <string>
#include <map>
#include <cstdlib>

class NTPaymentCGI {
private:
    std::map<std::string, std::string> params;
    
    void parseQueryString() {
        char* query = getenv("QUERY_STRING");
        if (!query) return;
        
        std::string queryStr(query);
        size_t pos = 0;
        
        while (pos < queryStr.length()) {
            size_t eq = queryStr.find('=', pos);
            if (eq == std::string::npos) break;
            
            size_t amp = queryStr.find('&', eq);
            if (amp == std::string::npos) amp = queryStr.length();
            
            std::string key = queryStr.substr(pos, eq - pos);
            std::string value = queryStr.substr(eq + 1, amp - eq - 1);
            
            params[key] = value;
            pos = amp + 1;
        }
    }
    
    std::string getParam(const std::string& name) {
        auto it = params.find(name);
        return (it != params.end()) ? it->second : "";
    }
    
    void outputHTML(const std::string& content) {
        printf("Content-Type: text/html; charset=UTF-8\n\n");
        printf("%s", content.c_str());
    }
    
    std::string generatePaymentPage(const std::string& paymentId) {
        // В реальной реализации здесь будет генерация уникального адреса
        std::string walletAddress = "TYourMainWalletAddressHere123456789";
        std::string memo = "PAY" + paymentId;
        std::string amount = getParam("amount");
        if (amount.empty()) amount = "10.00";
        
        return 
            "<html>\n"
            "<head>\n"
            "  <title>NTPayment - USDT TRC20 Payment</title>\n"
            "  <meta charset='UTF-8'>\n"
            "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n"
            "  <style>\n"
            "    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }\n"
            "    .container { background: white; padding: 30px; border-radius: 10px; max-width: 600px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n"
            "    .header { text-align: center; color: #333; margin-bottom: 30px; }\n"
            "    .payment-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }\n"
            "    .wallet-address { background: #e9ecef; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 10px 0; font-size: 14px; }\n"
            "    .amount { font-size: 24px; color: #28a745; font-weight: bold; text-align: center; margin: 20px 0; }\n"
            "    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }\n"
            "    .steps { margin: 20px 0; }\n"
            "    .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }\n"
            "    .copy-btn { background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 10px; }\n"
            "    .copy-btn:hover { background: #0056b3; }\n"
            "    .status { text-align: center; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; }\n"
            "  </style>\n"
            "  <script>\n"
            "    function copyToClipboard(text) {\n"
            "      navigator.clipboard.writeText(text).then(function() {\n"
            "        alert('Скопировано в буфер обмена!');\n"
            "      });\n"
            "    }\n"
            "    function checkPayment() {\n"
            "      // Здесь будет проверка статуса платежа\n"
            "      document.getElementById('status').innerHTML = 'Проверяем платеж...';\n"
            "      setTimeout(function() {\n"
            "        document.getElementById('status').innerHTML = 'Платеж не найден. Убедитесь, что отправили правильную сумму с указанным memo.';\n"
            "      }, 2000);\n"
            "    }\n"
            "  </script>\n"
            "</head>\n"
            "<body>\n"
            "  <div class='container'>\n"
            "    <div class='header'>\n"
            "      <h1>💰 USDT TRC20 Payment</h1>\n"
            "      <p>Payment ID: <strong>" + paymentId + "</strong></p>\n"
            "    </div>\n"
            "    \n"
            "    <div class='amount'>Amount to pay: " + amount + " USDT</div>\n"
            "    \n"
            "    <div class='payment-info'>\n"
            "      <h3>📍 Wallet Address (TRC20):</h3>\n"
            "      <div class='wallet-address'>" + walletAddress + "\n"
            "        <button class='copy-btn' onclick=\"copyToClipboard('" + walletAddress + "')\">Copy</button>\n"
            "      </div>\n"
            "      \n"
            "      <h3>🏷️ Payment Memo/Tag:</h3>\n"
            "      <div class='wallet-address'>" + memo + "\n"
            "        <button class='copy-btn' onclick=\"copyToClipboard('" + memo + "')\">Copy</button>\n"
            "      </div>\n"
            "    </div>\n"
            "    \n"
            "    <div class='warning'>\n"
            "      <strong>⚠️ Important:</strong>\n"
            "      <ul>\n"
            "        <li>Send exactly <strong>" + amount + " USDT</strong> (TRC20 network)</li>\n"
            "        <li>Include the memo/tag: <strong>" + memo + "</strong></li>\n"
            "        <li>Use only TRC20 network (Tron blockchain)</li>\n"
            "        <li>Payment will be confirmed automatically after 19 confirmations</li>\n"
            "      </ul>\n"
            "    </div>\n"
            "    \n"
            "    <div class='steps'>\n"
            "      <h3>📋 Payment Steps:</h3>\n"
            "      <div class='step'>1. Copy the wallet address above</div>\n"
            "      <div class='step'>2. Open your USDT wallet (TronLink, Trust Wallet, etc.)</div>\n"
            "      <div class='step'>3. Send exactly " + amount + " USDT to the address</div>\n"
            "      <div class='step'>4. Include the memo/tag: " + memo + "</div>\n"
            "      <div class='step'>5. Wait for confirmation (usually 1-3 minutes)</div>\n"
            "    </div>\n"
            "    \n"
            "    <div class='status' id='status'>\n"
            "      <p>Waiting for payment...<br>\n"
            "      <button onclick='checkPayment()' style='margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Check Payment Status</button></p>\n"
            "    </div>\n"
            "    \n"
            "    <div style='text-align: center; margin-top: 30px; color: #666;'>\n"
            "      <p>Payment will be processed automatically.<br>\n"
            "      You can close this page after sending the payment.</p>\n"
            "    </div>\n"
            "  </div>\n"
            "</body>\n"
            "</html>";
    }

public:
    void run() {
        parseQueryString();
        
        std::string paymentId = getParam("elid");
        if (paymentId.empty()) {
            // Попробуем другие возможные параметры
            paymentId = getParam("id");
            if (paymentId.empty()) {
                paymentId = getParam("payment_id");
            }
        }
        
        if (paymentId.empty()) {
            outputHTML("<html><body><h1>Error: Payment ID not provided</h1><p>Parameters received:</p><ul>");
            for (const auto& param : params) {
                printf("<li>%s = %s</li>", param.first.c_str(), param.second.c_str());
            }
            printf("</ul></body></html>");
            return;
        }
        
        std::string html = generatePaymentPage(paymentId);
        outputHTML(html);
    }
};

int main() {
    NTPaymentCGI cgi;
    cgi.run();
    return 0;
}
