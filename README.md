# NTPayment Module for BillManager 6

Модуль для приема платежей в USDT TRC20 (Tron blockchain) для BillManager 6.

## 🎯 Особенности

- ✅ Прием платежей в USDT TRC20 (Tron)
- ✅ Автоматическое подтверждение платежей
- ✅ Мониторинг блокчейна в реальном времени
- ✅ Настраиваемые лимиты и таймауты
- ✅ Поддержка тестового режима
- ✅ Shared library архитектура для BillManager 6

## 📋 Требования

- **BillManager 6**
- **g++ compiler**
- **make**
- **Root доступ**

## 🚀 Установка

```bash
# Простая установка
sudo bash install_final.sh

# Или с указанием пути к BillManager
sudo bash install_final.sh /usr/local/mgr5
```

## ⚙️ Настройка

1. Войдите в BillManager 6 админ-панель
2. Перейдите: **Настройки → Методы оплаты**
3. Нажмите **"Добавить"** → выберите **"NTPayment"**
4. Заполните поля:

```
URL узла Tron: https://api.trongrid.io
Адрес контракта USDT: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
Адрес кошелька для получения: ваш_tron_адрес
Приватный ключ кошелька: ваш_приватный_ключ
Требуемые подтверждения: 19
Таймаут платежа: 60
Тестовый режим: ✓ (для тестирования)
```

## 🔐 Безопасность

⚠️ **ВАЖНО:**
- Используйте отдельный кошелек только для приема платежей
- Регулярно выводите средства на холодный кошелек
- Никогда не делитесь приватным ключом
- Включите тестовый режим для проверки

## 📁 Структура проекта

```
├── src/                          # Исходный код C++
│   ├── ntpayment.cpp            # Основной модуль
│   └── ntpayment_cgi.cpp        # CGI обработчик
├── etc/xml/                     # XML конфигурация
│   └── billmgr_mod_pmntpayment_final.xml
├── include/php/                 # PHP утилиты
│   └── usdt_util.php
├── cgi/                         # CGI скрипты
│   └── ntnotify
├── Makefile                     # Система сборки
├── install_final.sh             # Установщик
└── README.md                    # Документация
```

## 🛠️ Сборка

```bash
# Сборка всех компонентов
make all

# Очистка
make clean

# Установка
make install BILLMGR_PATH=/usr/local/mgr5
```

## 🎯 Готово!

Модуль полностью совместим с BillManager 6 и готов к использованию для приема USDT TRC20 платежей.

---

**🚀 Успешной работы с NTPayment!**
