<?xml version="1.0" encoding="UTF-8"?>
<mgrdata>
  <plugin name="pmntpayment">
    <group>payment_method</group>
    <author>Custom Development</author>
    <params>
      <priority lang="ru">500</priority>
      <priority lang="en">500</priority>
    </params>
  </plugin>
  <metadata name="paymethod.edit.pmntpayment" type="form">
    <form>
      <page name="methodprops">
        <field name="tron_node_url">
          <input type="text" name="tron_node_url" required="yes"/>
        </field>
        <field name="usdt_contract_address">
          <input type="text" name="usdt_contract_address" required="yes"/>
        </field>
        <field name="wallet_address">
          <input type="text" name="wallet_address" required="yes"/>
        </field>
        <field name="wallet_private_key">
          <input type="text" name="wallet_private_key" private="yes" required="yes"/>
        </field>
        <field name="confirmations_required">
          <input type="text" name="confirmations_required"/>
        </field>
        <field name="timeout_minutes">
          <input type="text" name="timeout_minutes"/>
        </field>
        <field name="min_amount">
          <input type="text" name="min_amount"/>
        </field>
        <field name="max_amount">
          <input type="text" name="max_amount"/>
        </field>
        <field name="test">
          <input type="checkbox" name="test"/>
        </field>
      </page>
    </form>
  </metadata>
  <lang name="ru">
    <messages name="plugin">
      <msg name="desc_short_pmntpayment">NTPayment</msg>
      <msg name="desc_full_pmntpayment">Прием платежей в USDT TRC20 через сеть Tron</msg>
      <msg name="price_pmntpayment">Бесплатно</msg>
    </messages>
    <messages name="label_paymethod">
      <msg name="pmntpayment">NTPayment</msg>
      <msg name="module_pmntpayment">NTPayment</msg>
    </messages>
    <messages name="paymethod.edit.pmntpayment">
      <msg name="header">Настройки NTPayment</msg>
      <msg name="methodprops">Настройки способа оплаты</msg>
      <msg name="tron_node_url">URL узла Tron</msg>
      <msg name="hint_tron_node_url">API endpoint для взаимодействия с блокчейном Tron. Пример: https://api.trongrid.io</msg>
      <msg name="usdt_contract_address">Адрес контракта USDT TRC20</msg>
      <msg name="hint_usdt_contract_address">Официальный адрес смарт-контракта USDT в сети Tron: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t</msg>
      <msg name="wallet_address">Адрес кошелька для получения</msg>
      <msg name="hint_wallet_address">Ваш Tron-адрес для получения USDT платежей. Должен поддерживать TRC20 токены</msg>
      <msg name="wallet_private_key">Приватный ключ кошелька</msg>
      <msg name="hint_wallet_private_key">Приватный ключ для подписи транзакций (64 символа). ДЕРЖИТЕ В СЕКРЕТЕ!</msg>
      <msg name="confirmations_required">Требуемые подтверждения</msg>
      <msg name="hint_confirmations_required">Количество подтверждений блокчейна для считания платежа завершенным (рекомендуется: 19)</msg>
      <msg name="timeout_minutes">Таймаут платежа (минуты)</msg>
      <msg name="hint_timeout_minutes">Время ожидания платежа в минутах (по умолчанию: 60)</msg>
      <msg name="min_amount">Минимальная сумма (USDT)</msg>
      <msg name="hint_min_amount">Минимальная сумма платежа в USDT (по умолчанию: 1.0)</msg>
      <msg name="max_amount">Максимальная сумма (USDT)</msg>
      <msg name="hint_max_amount">Максимальная сумма платежа в USDT (оставьте пустым для отсутствия лимита)</msg>
      <msg name="test">Тестовый режим</msg>
      <msg name="hint_test">Включает режим отладки и расширенное логирование</msg>
    </messages>
  </lang>
  <lang name="en">
    <messages name="plugin">
      <msg name="desc_short_pmntpayment">NTPayment</msg>
      <msg name="desc_full_pmntpayment">Accept USDT TRC20 payments via Tron network</msg>
      <msg name="price_pmntpayment">Free</msg>
    </messages>
    <messages name="label_paymethod">
      <msg name="pmntpayment">NTPayment</msg>
      <msg name="module_pmntpayment">NTPayment</msg>
    </messages>
    <messages name="paymethod.edit.pmntpayment">
      <msg name="header">NTPayment Settings</msg>
      <msg name="methodprops">Payment Method Settings</msg>
      <msg name="tron_node_url">Tron Node URL</msg>
      <msg name="hint_tron_node_url">API endpoint for Tron blockchain interaction. Example: https://api.trongrid.io</msg>
      <msg name="usdt_contract_address">USDT TRC20 Contract Address</msg>
      <msg name="hint_usdt_contract_address">Official USDT smart contract address on Tron network: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t</msg>
      <msg name="wallet_address">Receiving Wallet Address</msg>
      <msg name="hint_wallet_address">Your Tron address for receiving USDT payments. Must support TRC20 tokens</msg>
      <msg name="wallet_private_key">Wallet Private Key</msg>
      <msg name="hint_wallet_private_key">Private key for transaction signing (64 characters). KEEP SECRET!</msg>
      <msg name="confirmations_required">Required Confirmations</msg>
      <msg name="hint_confirmations_required">Number of blockchain confirmations to consider payment complete (recommended: 19)</msg>
      <msg name="timeout_minutes">Payment Timeout (minutes)</msg>
      <msg name="hint_timeout_minutes">Payment waiting time in minutes (default: 60)</msg>
      <msg name="min_amount">Minimum Amount (USDT)</msg>
      <msg name="hint_min_amount">Minimum payment amount in USDT (default: 1.0)</msg>
      <msg name="max_amount">Maximum Amount (USDT)</msg>
      <msg name="hint_max_amount">Maximum payment amount in USDT (leave empty for no limit)</msg>
      <msg name="test">Test Mode</msg>
      <msg name="hint_test">Enables debug mode and extended logging</msg>
    </messages>
  </lang>
</mgrdata>
