<?xml version="1.0" encoding="UTF-8"?>
<mgrdata>
  <plugin name="pmntpayment" type="python">
    <metadata name="pmntpayment" type="module">
      <module>
        <name>pmntpayment</name>
        <group>payment_method</group>
        <python>pmntpayment.py</python>
      </module>
    </metadata>
    
    <metadata name="pmntpayment" type="form">
      <form>
        <field name="name">
          <input name="name"/>
        </field>
        <field name="tron_node_url">
          <input name="tron_node_url"/>
        </field>
        <field name="usdt_contract_address">
          <input name="usdt_contract_address"/>
        </field>
        <field name="wallet_address">
          <input name="wallet_address"/>
        </field>
        <field name="wallet_private_key">
          <input name="wallet_private_key"/>
        </field>
        <field name="test_mode">
          <checkbox name="test_mode"/>
        </field>
      </form>
    </metadata>
  </plugin>
  
  <lang name="en">
    <messages name="pmntpayment">
      <msg name="name">NTPayment</msg>
      <msg name="tron_node_url">Tron Node URL</msg>
      <msg name="usdt_contract_address">USDT Contract Address</msg>
      <msg name="wallet_address">Wallet Address</msg>
      <msg name="wallet_private_key">Wallet Private Key</msg>
      <msg name="test_mode">Test Mode</msg>
      <msg name="hint_tron_node_url">Tron blockchain node URL (e.g., https://api.trongrid.io)</msg>
      <msg name="hint_usdt_contract_address">USDT TRC20 contract address (TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t)</msg>
      <msg name="hint_wallet_address">Your USDT TRC20 wallet address</msg>
      <msg name="hint_wallet_private_key">Private key for wallet address</msg>
      <msg name="hint_test_mode">Enable test mode for development</msg>
    </messages>
  </lang>
  
  <lang name="ru">
    <messages name="pmntpayment">
      <msg name="name">NTPayment</msg>
      <msg name="tron_node_url">URL узла Tron</msg>
      <msg name="usdt_contract_address">Адрес контракта USDT</msg>
      <msg name="wallet_address">Адрес кошелька</msg>
      <msg name="wallet_private_key">Приватный ключ кошелька</msg>
      <msg name="test_mode">Тестовый режим</msg>
      <msg name="hint_tron_node_url">URL узла блокчейна Tron (например, https://api.trongrid.io)</msg>
      <msg name="hint_usdt_contract_address">Адрес контракта USDT TRC20 (TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t)</msg>
      <msg name="hint_wallet_address">Ваш адрес кошелька USDT TRC20</msg>
      <msg name="hint_wallet_private_key">Приватный ключ для адреса кошелька</msg>
      <msg name="hint_test_mode">Включить тестовый режим для разработки</msg>
    </messages>
  </lang>
</mgrdata>
