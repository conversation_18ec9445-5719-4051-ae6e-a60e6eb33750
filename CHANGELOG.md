# Changelog

All notable changes to the NTPayment module will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-01-16

### Added
- Initial release of NTPayment module for BillManager 6
- USDT TRC20 payment processing via Tron blockchain
- Shared library architecture compatible with BillManager 6
- Automatic payment confirmation system
- Real-time blockchain monitoring
- Configurable confirmation requirements and timeouts
- Test mode support
- Comprehensive XML configuration with proper field labels
- Simple installation script
- C++ implementation for better performance and security

### Features
- **Payment Processing**: Accept USDT TRC20 payments directly
- **Blockchain Integration**: Direct integration with Tron network
- **Auto-confirmation**: Automatic payment verification and confirmation
- **Configurable Settings**: Customizable timeouts, confirmations, and limits
- **Security**: Private key handling and secure transaction processing
- **Logging**: Detailed logging for debugging and monitoring
- **Multi-language**: Support for Russian and English interfaces

### Technical Details
- Compatible with BillManager 6 wrapper system
- Uses shared libraries (.so files) instead of executables
- Proper XML metadata structure for BillManager 6
- CGI scripts for payment processing
- PHP utilities for USDT/Tron operations

### Installation
- Simple one-command installation script
- Automatic file placement and permission setting
- BillManager service restart handling
- Backup creation during installation

### Security
- Private key encryption and secure storage
- Separate wallet recommendation for payment processing
- Test mode for safe configuration testing
- Input validation and sanitization
