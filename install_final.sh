#!/bin/bash

# Simple NTPayment Installer for BillManager 6
# Простой установщик NTPayment для BillManager 6

BILLMGR_PATH="${1:-/usr/local/mgr5}"

echo "=== NTPayment Installer for BillManager 6 ==="
echo "Installing to: $BILLMGR_PATH"
echo

# Build module
echo "Building module..."
make clean
make all

if [ ! -f "libexec/pmntpayment.so" ] || [ ! -f "libexec/ntpayment.so" ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo "Build successful"

# Install files
echo "Installing files..."

# XML configuration
sudo cp etc/xml/billmgr_mod_pmntpayment_final.xml $BILLMGR_PATH/etc/xml/billmgr_mod_pmntpayment.xml

# Shared libraries
sudo cp libexec/pmntpayment.so $BILLMGR_PATH/libexec/pmntpayment.so
sudo cp libexec/ntpayment.so $BILLMGR_PATH/libexec/ntpayment.so

# Wrapper links
sudo rm -f $BILLMGR_PATH/paymethods/pmntpayment
sudo rm -f $BILLMGR_PATH/cgi/ntpayment
sudo ln -s ../libexec/wrapper $BILLMGR_PATH/paymethods/pmntpayment
sudo ln -s ../libexec/wrapper $BILLMGR_PATH/cgi/ntpayment

# Notification script
if [ -f "cgi/ntnotify" ]; then
    sudo cp cgi/ntnotify $BILLMGR_PATH/cgi/ntnotify
    sudo chmod 755 $BILLMGR_PATH/cgi/ntnotify
    sudo chown root:root $BILLMGR_PATH/cgi/ntnotify
else
    echo "Warning: cgi/ntnotify not found, skipping"
fi

# Install logos if available
if [ -f "assets/logo/ntpayment_64.png" ]; then
    echo "Installing logos..."
    sudo mkdir -p $BILLMGR_PATH/theme/default/images/
    sudo cp assets/logo/ntpayment_64.png $BILLMGR_PATH/theme/default/images/pmntpayment.png
    sudo cp assets/logo/ntpayment_64.png $BILLMGR_PATH/theme/default/images/pmntpayment_64.png
    sudo chmod 644 $BILLMGR_PATH/theme/default/images/pmntpayment*.png
    sudo chown root:root $BILLMGR_PATH/theme/default/images/pmntpayment*.png
    echo "Logos installed"
fi

# Set permissions
sudo chmod 644 $BILLMGR_PATH/etc/xml/billmgr_mod_pmntpayment.xml
sudo chmod 755 $BILLMGR_PATH/libexec/pmntpayment.so
sudo chmod 755 $BILLMGR_PATH/libexec/ntpayment.so
sudo chmod 755 $BILLMGR_PATH/cgi/ntnotify
sudo chown root:root $BILLMGR_PATH/etc/xml/billmgr_mod_pmntpayment.xml
sudo chown root:root $BILLMGR_PATH/libexec/pmntpayment.so
sudo chown root:root $BILLMGR_PATH/libexec/ntpayment.so
sudo chown root:root $BILLMGR_PATH/cgi/ntnotify

# Clear cache
sudo rm -rf $BILLMGR_PATH/var/.xmlcache/*

echo "Files installed"

# Restart BillManager
echo "Restarting BillManager..."
sudo $BILLMGR_PATH/sbin/mgrctl -m billmgr exit
sleep 5

echo
echo "=== Installation Complete ==="
echo
echo "Next steps:"
echo "1. Go to BillManager admin panel"
echo "2. Settings → Payment Methods"
echo "3. Add → NTPayment"
echo "4. Configure settings:"
echo "   - URL узла Tron: https://api.trongrid.io"
echo "   - Адрес контракта USDT: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
echo "   - Your wallet address and private key"
echo "   - Test mode: ON"
echo
echo "Done!"
