#include <iostream>
#include <cstdio>
#include <cstring>

extern "C" int ispmain(int argc, char* argv[]) {
    printf("Content-Type: text/xml\n\n");
    printf("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
    printf("<doc>\n");
    
    if (argc > 1 && strcmp(argv[1], "--command") == 0 && argc > 2) {
        if (strcmp(argv[2], "config") == 0) {
            printf("  <ok/>\n");
            printf("  <pmntpayment>\n");
            printf("    <name>NTPayment</name>\n");
            printf("    <version>1.0</version>\n");
            printf("  </pmntpayment>\n");
        } else {
            printf("  <ok/>\n");
        }
    } else {
        // Информация о модуле
        printf("  <feature>\n");
        printf("    <redirect>on</redirect>\n");
        printf("    <pmtune>on</pmtune>\n");
        printf("    <pmvalidate>on</pmvalidate>\n");
        printf("    <crtune>on</crtune>\n");
        printf("    <crvalidate>on</crvalidate>\n");
        printf("    <crset>on</crset>\n");
        printf("    <crdelete>on</crdelete>\n");
        printf("  </feature>\n");
        printf("  <param>\n");
        printf("    <payment_script>/mancgi/ntpayment</payment_script>\n");
        printf("  </param>\n");
    }
    
    printf("</doc>\n");
    return 0;
}

extern "C" int main(int argc, char* argv[]) {
    return ispmain(argc, argv);
}
